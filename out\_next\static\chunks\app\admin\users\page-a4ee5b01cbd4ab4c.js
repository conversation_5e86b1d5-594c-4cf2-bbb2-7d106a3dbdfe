(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6779,8733,9160],{2899:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var i=a(5155),o=a(2115),n=a(6874),r=a.n(n),s=a(6681),l=a(6779),d=a(6273),c=a(3592),u=a(3737),g=a(4752),m=a.n(g);function p(){let{user:e,loading:t,isAdmin:a}=(0,s.wC)(),[n,g]=(0,o.useState)([]),[p,x]=(0,o.useState)(!0),[h,f]=(0,o.useState)(""),[y,v]=(0,o.useState)(!1),[b,w]=(0,o.useState)(0),[D,j]=(0,o.useState)(null),[N,E]=(0,o.useState)(!1),[A,k]=(0,o.useState)({name:"",email:"",mobile:"",referralCode:"",referredBy:"",plan:"",activeDays:0,totalVideos:0,todayVideos:0,wallet:0,status:"active",videoDuration:300,quickVideoAdvantage:!1,quickVideoAdvantageDays:7,quickVideoAdvantageSeconds:30}),[S,C]=(0,o.useState)(!1),[L,V]=(0,o.useState)(1),[I,q]=(0,o.useState)(!0),[F,M]=(0,o.useState)(null);(0,o.useEffect)(()=>{a&&T()},[a]);let T=async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];try{x(!0);try{console.log("\uD83D\uDE80 Loading admin users with optimized function...");let t=await d.x8.getAdminUsers({page:e?1:L+1,limit:50,searchTerm:h,planFilter:"all",sortBy:"joinedDate",sortOrder:"desc"});e?(g(t.users),V(1),w(t.totalUsers)):(g(e=>[...e,...t.users]),V(e=>e+1)),q(t.hasMore),console.log("✅ Admin users loaded via optimized function")}catch(a){console.warn("⚠️ Optimized function failed, using fallback:",a);let t=await (0,l.lo)(50,e?null:F);if(e){g(t.users),V(1);try{let e=await (0,l.nQ)();w(e)}catch(e){console.error("Error getting total user count:",e)}}else g(e=>[...e,...t.users]);M(t.lastDoc),q(t.hasMore)}}catch(e){console.error("Error loading users:",e),m().fire({icon:"error",title:"Error",text:"Failed to load users. Please try again."})}finally{x(!1)}},U=async()=>{if(!h.trim())return void T();try{v(!0);let e=await (0,l.x5)(h.trim());g(e),q(!1)}catch(e){console.error("Error searching users:",e),m().fire({icon:"error",title:"Search Failed",text:"Failed to search users. Please try again."})}finally{v(!1)}},B=e=>{j(e),k({name:e.name,email:e.email,mobile:e.mobile,referralCode:e.referralCode,referredBy:e.referredBy,plan:e.plan,activeDays:e.activeDays,totalVideos:e.totalVideos,todayVideos:e.todayVideos,wallet:e.wallet||0,status:e.status,videoDuration:e.videoDuration||300,quickVideoAdvantage:e.quickVideoAdvantage||!1,quickVideoAdvantageDays:e.quickVideoAdvantageDays||7,quickVideoAdvantageSeconds:e.quickVideoAdvantageSeconds||30}),E(!0)},O=async()=>{if(D)try{C(!0);let t=D.plan,a=A.plan,i=t!==a,o=A.activeDays!==D.activeDays,n={name:A.name,email:A.email,mobile:A.mobile,referralCode:A.referralCode,referredBy:A.referredBy,plan:A.plan,activeDays:A.activeDays,totalVideos:A.totalVideos,todayVideos:A.todayVideos,wallet:A.wallet,status:A.status};if(o){n.manuallySetActiveDays=!0;let e=new Date,t=new Date(e.getTime()-(A.activeDays-1)*864e5);n.joinedDate=t,console.log("Active days manually set to ".concat(A.activeDays," for user ").concat(D.id)),console.log("Recalculated joined date to ".concat(t.toLocaleDateString()," to match active days"))}await (0,l.TK)(D.id,n),A.videoDuration!==(D.videoDuration||300)&&await (0,c.Gl)(D.id,A.videoDuration);let r=!!D.quickVideoAdvantage;if(A.quickVideoAdvantage&&!r?await (0,c.w1)(D.id,A.quickVideoAdvantageDays,(null==e?void 0:e.email)||"admin",A.quickVideoAdvantageSeconds):!A.quickVideoAdvantage&&r?await (0,c.wT)(D.id,(null==e?void 0:e.email)||"admin"):A.quickVideoAdvantage&&r&&(await (0,c.wT)(D.id,(null==e?void 0:e.email)||"admin"),await (0,c.w1)(D.id,A.quickVideoAdvantageDays,(null==e?void 0:e.email)||"admin",A.quickVideoAdvantageSeconds)),i)try{await (0,c.II)(D.id,a),o?console.log("Plan changed but active days manually set to ".concat(A.activeDays," for user ").concat(D.id)):(n.activeDays=1,n.manuallySetActiveDays=!1,console.log("Reset active days to 1 for user ".concat(D.id," due to plan change: ").concat(t," -> ").concat(a))),console.log("Updated plan expiry for user ".concat(D.id,": ").concat(t," -> ").concat(a))}catch(e){console.error("Error updating plan expiry:",e)}if(i&&"Trial"===t&&"Trial"!==a)try{console.log("Processing referral bonus for user ".concat(D.id,": ").concat(t," -> ").concat(a)),await (0,c.IK)(D.id,t,a),m().fire({icon:"success",title:"User Updated & Referral Bonus Processed",html:'\n              <div class="text-left">\n                <p><strong>User plan updated:</strong> '.concat(t," → ").concat(a,"</p>\n                <p><strong>Referral bonus:</strong> Processed for referrer (if applicable)</p>\n              </div>\n            "),timer:4e3,showConfirmButton:!1})}catch(e){console.error("Error processing referral bonus:",e),m().fire({icon:"warning",title:"User Updated (Referral Bonus Issue)",html:'\n              <div class="text-left">\n                <p><strong>User plan updated successfully:</strong> '.concat(t," → ").concat(a,'</p>\n                <p><strong>Referral bonus:</strong> Could not be processed automatically</p>\n                <p class="text-sm text-gray-600 mt-2">Please check referral bonus manually if needed.</p>\n              </div>\n            '),timer:5e3,showConfirmButton:!1})}else{let e="User information has been updated successfully";A.quickVideoAdvantage&&!r?e+=". Quick video advantage granted for ".concat(A.quickVideoAdvantageDays," days."):!A.quickVideoAdvantage&&r?e+=". Quick video advantage removed.":A.quickVideoAdvantage&&r&&(e+=". Quick video advantage updated for ".concat(A.quickVideoAdvantageDays," days.")),m().fire({icon:"success",title:"User Updated",text:e,timer:3e3,showConfirmButton:!1})}g(e=>e.map(e=>e.id===D.id?{...e,...n,videoDuration:A.videoDuration,quickVideoAdvantage:A.quickVideoAdvantage,quickVideoAdvantageDays:A.quickVideoAdvantage?A.quickVideoAdvantageDays:0,quickVideoAdvantageSeconds:A.quickVideoAdvantage?A.quickVideoAdvantageSeconds:30,quickVideoAdvantageExpiry:A.quickVideoAdvantage?new Date(Date.now()+24*A.quickVideoAdvantageDays*36e5):null}:e)),E(!1),j(null),await T()}catch(e){console.error("Error updating user:",e),m().fire({icon:"error",title:"Update Failed",text:"Failed to update user. Please try again."})}finally{C(!1)}},P=async e=>{if((await m().fire({icon:"warning",title:"Delete User",text:"Are you sure you want to delete ".concat(e.name,"? This action cannot be undone."),showCancelButton:!0,confirmButtonText:"Yes, Delete",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{await (0,l.hG)(e.id),g(t=>t.filter(t=>t.id!==e.id)),m().fire({icon:"success",title:"User Deleted",text:"User has been deleted successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting user:",e),m().fire({icon:"error",title:"Delete Failed",text:"Failed to delete user. Please try again."})}},z=e=>null==e||isNaN(e)?"₹0.00":"₹".concat(e.toFixed(2)),_=e=>{switch(e){case"Trial":default:return"bg-gray-500";case"Starter":return"bg-blue-500";case"Basic":return"bg-green-500";case"Premium":return"bg-purple-500";case"Gold":return"bg-yellow-500";case"Platinum":return"bg-indigo-500";case"Diamond":return"bg-pink-500"}},R=e=>{switch(e){case"active":return"bg-green-500";case"inactive":return"bg-red-500";case"suspended":return"bg-yellow-500";default:return"bg-gray-500"}},Q=async()=>{try{m().fire({title:"Exporting Users...",text:"Please wait while we prepare your export file.",allowOutsideClick:!1,didOpen:()=>{m().showLoading()}});let e=await (0,l.CF)();if(0===e.length)return void m().fire({icon:"warning",title:"No Data",text:"No users to export."});console.log("\uD83D\uDCCA Exporting users with stored active days from Firestore...");let t=(0,u.Fz)(e);(0,u.Bf)(t,"users"),m().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(e.length," users to CSV file."),timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error exporting users:",e),m().fire({icon:"error",title:"Export Failed",text:"Failed to export users. Please try again."})}};return t?(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,i.jsx)("div",{className:"spinner"})}):(0,i.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,i.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,i.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,i.jsxs)(r(),{href:"/admin",className:"text-gray-600 hover:text-gray-800",children:[(0,i.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"User Management"}),b>0&&(0,i.jsx)("p",{className:"text-sm text-gray-600",children:h?"Showing ".concat(n.length," of ").concat(b," users"):"Total: ".concat(b," users")})]}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsxs)(r(),{href:"/admin/upload-users",className:"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 inline-flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload Users"]}),(0,i.jsxs)("button",{onClick:Q,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700",children:[(0,i.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,i.jsxs)("button",{onClick:()=>T(),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:[(0,i.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,i.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,i.jsxs)("div",{className:"flex gap-4",children:[(0,i.jsx)("input",{type:"text",value:h,onChange:e=>f(e.target.value),placeholder:"Search by name, email, mobile, or referral code...",className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",onKeyDown:e=>"Enter"===e.key&&U()}),(0,i.jsx)("button",{onClick:U,disabled:y,className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50",children:y?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Searching..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("i",{className:"fas fa-search mr-2"}),"Search"]})}),h&&(0,i.jsx)("button",{onClick:()=>{f(""),T()},className:"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700",children:(0,i.jsx)("i",{className:"fas fa-times"})})]})}),(0,i.jsx)("div",{className:"p-6",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,i.jsx)("thead",{className:"bg-gray-50",children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Contact"}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plan"}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Videos"}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Duration"}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Quick Advantage"}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Wallet"}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Referrals"}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,i.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:p&&0===n.length?(0,i.jsx)("tr",{children:(0,i.jsxs)("td",{colSpan:10,className:"px-6 py-4 text-center",children:[(0,i.jsx)("div",{className:"spinner mx-auto"}),(0,i.jsx)("p",{className:"mt-2 text-gray-500",children:"Loading users..."})]})}):0===n.length?(0,i.jsx)("tr",{children:(0,i.jsx)("td",{colSpan:10,className:"px-6 py-4 text-center text-gray-500",children:"No users found"})}):n.map(e=>(0,i.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,i.jsx)("div",{className:"text-sm text-gray-500",children:e.email}),(0,i.jsxs)("div",{className:"text-sm text-gray-500",children:["Joined: ",(()=>{try{let t;if(!e.joinedDate)return"No Date";if(e.joinedDate instanceof Date)t=e.joinedDate;else if("object"==typeof e.joinedDate&&e.joinedDate&&"toDate"in e.joinedDate)t=e.joinedDate.toDate();else{if("string"!=typeof e.joinedDate&&"number"!=typeof e.joinedDate)return"Invalid Format";t=new Date(e.joinedDate)}if(isNaN(t.getTime()))return"Invalid Date";return t.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}catch(t){return console.error("Error formatting joined date for user:",e.id,t),"Date Error"}})()]})]})}),(0,i.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,i.jsx)("div",{className:"text-sm text-gray-900",children:e.mobile}),(0,i.jsxs)("div",{className:"text-sm text-gray-500",children:["Code: ",e.referralCode]}),e.referredBy&&(0,i.jsxs)("div",{className:"text-sm text-gray-500",children:["Ref: ",e.referredBy]})]}),(0,i.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,i.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white ".concat(_(e.plan)),children:e.plan}),(0,i.jsxs)("div",{className:"text-sm text-gray-500 mt-1",children:["Days: ",e.activeDays]})]}),(0,i.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,i.jsxs)("div",{className:"text-sm text-gray-900",children:["Total: ",e.totalVideos]}),(0,i.jsxs)("div",{className:"text-sm text-gray-500",children:["Today: ",e.todayVideos]})]}),(0,i.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,i.jsx)("div",{className:"text-sm text-gray-900",children:60>(e.videoDuration||300)?"".concat(e.videoDuration||300,"s"):"".concat(Math.round((e.videoDuration||300)/60),"m")}),(0,i.jsx)("div",{className:"text-xs text-gray-500",children:60>(e.videoDuration||300)?"".concat(e.videoDuration||300," second").concat((e.videoDuration||300)>1?"s":""):"".concat(Math.round((e.videoDuration||300)/60)," minute").concat(Math.round((e.videoDuration||300)/60)>1?"s":"")})]}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.quickVideoAdvantage&&(e.quickVideoAdvantageRemainingDays&&e.quickVideoAdvantageRemainingDays>0||e.quickVideoAdvantageExpiry&&new Date<e.quickVideoAdvantageExpiry)?(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white bg-green-500",children:"Active"}),(0,i.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:void 0!==e.quickVideoAdvantageRemainingDays?"".concat(e.quickVideoAdvantageRemainingDays," days left"):e.quickVideoAdvantageExpiry?"Until: ".concat(e.quickVideoAdvantageExpiry instanceof Date?e.quickVideoAdvantageExpiry.toLocaleDateString():new Date(e.quickVideoAdvantageExpiry).toLocaleDateString()):"Active"})]}):(0,i.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white bg-gray-500",children:"None"})}),(0,i.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,i.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,i.jsx)("i",{className:"fas fa-wallet mr-1 text-green-500"}),z(e.wallet||0)]}),(0,i.jsx)("div",{className:"text-xs text-gray-500",children:"Total Balance"})]}),(0,i.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,i.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,i.jsx)("i",{className:"fas fa-users mr-1 text-blue-500"}),e.referralsCount||0]}),(0,i.jsxs)("div",{className:"text-xs text-gray-500",children:["Non-Trial: ",e.referralsDoneExcludingTrial||0]})]}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,i.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white ".concat(R(e.status)),children:e.status})}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsx)("button",{onClick:()=>B(e),className:"text-blue-600 hover:text-blue-900",title:"Edit User",children:(0,i.jsx)("i",{className:"fas fa-edit"})}),(0,i.jsx)("button",{onClick:()=>P(e),className:"text-red-600 hover:text-red-900",title:"Delete User",children:(0,i.jsx)("i",{className:"fas fa-trash"})})]})})]},e.id))})]})}),I&&!p&&n.length>0&&(0,i.jsx)("div",{className:"px-6 py-4 border-t border-gray-200 text-center",children:(0,i.jsxs)("button",{onClick:()=>{I&&!p&&T(!1)},className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:[(0,i.jsx)("i",{className:"fas fa-chevron-down mr-2"}),"Load More Users"]})})]})}),N&&D&&(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 p-4 overflow-y-auto modal-scrollable",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg w-full max-w-md my-8 max-h-[calc(100vh-2rem)] flex flex-col shadow-xl",children:[(0,i.jsx)("div",{className:"p-6 border-b border-gray-200 flex-shrink-0",children:(0,i.jsx)("h3",{className:"text-lg font-bold text-gray-900",children:"Edit User"})}),(0,i.jsxs)("div",{className:"p-6 space-y-4 flex-1 overflow-y-auto modal-scrollable",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),(0,i.jsx)("input",{type:"text",value:A.name,onChange:e=>k(t=>({...t,name:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,i.jsx)("input",{type:"email",value:A.email,onChange:e=>k(t=>({...t,email:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Mobile"}),(0,i.jsx)("input",{type:"text",value:A.mobile,onChange:e=>k(t=>({...t,mobile:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Referral Code"}),(0,i.jsx)("input",{type:"text",value:A.referralCode,onChange:e=>k(t=>({...t,referralCode:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Referred By"}),(0,i.jsx)("input",{type:"text",value:A.referredBy,onChange:e=>k(t=>({...t,referredBy:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Plan"}),(0,i.jsxs)("select",{value:A.plan,onChange:e=>k(t=>({...t,plan:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,i.jsx)("option",{value:"Trial",children:"Trial"}),(0,i.jsx)("option",{value:"Starter",children:"Starter"}),(0,i.jsx)("option",{value:"Basic",children:"Basic"}),(0,i.jsx)("option",{value:"Premium",children:"Premium"}),(0,i.jsx)("option",{value:"Gold",children:"Gold"}),(0,i.jsx)("option",{value:"Platinum",children:"Platinum"}),(0,i.jsx)("option",{value:"Diamond",children:"Diamond"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Active Days"}),(0,i.jsx)("input",{type:"number",value:A.activeDays,onChange:e=>k(t=>({...t,activeDays:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Total Videos"}),(0,i.jsx)("input",{type:"number",value:A.totalVideos,onChange:e=>k(t=>({...t,totalVideos:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Today Videos"}),(0,i.jsx)("input",{type:"number",value:A.todayVideos,onChange:e=>k(t=>({...t,todayVideos:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Wallet Balance (₹)"}),(0,i.jsx)("input",{type:"number",step:"0.01",value:A.wallet,onChange:e=>k(t=>({...t,wallet:parseFloat(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Video Duration"}),(0,i.jsxs)("select",{value:A.videoDuration,onChange:e=>k(t=>({...t,videoDuration:parseInt(e.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,i.jsxs)("optgroup",{label:"\uD83D\uDE80 Quick Duration",children:[(0,i.jsx)("option",{value:1,children:"1 second"}),(0,i.jsx)("option",{value:10,children:"10 seconds"}),(0,i.jsx)("option",{value:30,children:"30 seconds"})]}),(0,i.jsxs)("optgroup",{label:"⏱️ Standard Duration",children:[(0,i.jsx)("option",{value:60,children:"1 minute"}),(0,i.jsx)("option",{value:120,children:"2 minutes"}),(0,i.jsx)("option",{value:180,children:"3 minutes"}),(0,i.jsx)("option",{value:240,children:"4 minutes"}),(0,i.jsx)("option",{value:300,children:"5 minutes"}),(0,i.jsx)("option",{value:360,children:"6 minutes"}),(0,i.jsx)("option",{value:420,children:"7 minutes"}),(0,i.jsx)("option",{value:600,children:"10 minutes"})]})]}),(0,i.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:A.videoDuration<60?"".concat(A.videoDuration," second").concat(A.videoDuration>1?"s":""):"".concat(Math.round(A.videoDuration/60)," minute").concat(Math.round(A.videoDuration/60)>1?"s":"")})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,i.jsxs)("select",{value:A.status,onChange:e=>k(t=>({...t,status:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,i.jsx)("option",{value:"active",children:"Active"}),(0,i.jsx)("option",{value:"inactive",children:"Inactive"}),(0,i.jsx)("option",{value:"suspended",children:"Suspended"})]})]}),(0,i.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,i.jsxs)("h4",{className:"text-md font-semibold text-gray-900 mb-3",children:[(0,i.jsx)("i",{className:"fas fa-bolt mr-2 text-yellow-500"}),"Quick Video Advantage"]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",id:"quickVideoAdvantage",checked:A.quickVideoAdvantage,onChange:e=>k(t=>({...t,quickVideoAdvantage:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,i.jsx)("label",{htmlFor:"quickVideoAdvantage",className:"ml-2 block text-sm text-gray-700",children:"Grant Quick Video Advantage"})]}),A.quickVideoAdvantage&&(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4 ml-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Days"}),(0,i.jsx)("input",{type:"number",min:"1",max:"365",value:A.quickVideoAdvantageDays,onChange:e=>k(t=>({...t,quickVideoAdvantageDays:parseInt(e.target.value)||7})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Video Duration"}),(0,i.jsxs)("select",{value:A.quickVideoAdvantageSeconds,onChange:e=>k(t=>({...t,quickVideoAdvantageSeconds:parseInt(e.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,i.jsx)("option",{value:1,children:"1 second"}),(0,i.jsx)("option",{value:10,children:"10 seconds"}),(0,i.jsx)("option",{value:30,children:"30 seconds"})]})]})]}),D&&(0,i.jsx)("div",{className:"ml-6 p-3 bg-gray-50 rounded-lg",children:(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,i.jsx)("strong",{children:"Current Status:"})," ",D.quickVideoAdvantage&&(D.quickVideoAdvantageRemainingDays&&D.quickVideoAdvantageRemainingDays>0||D.quickVideoAdvantageExpiry&&new Date<D.quickVideoAdvantageExpiry)?(0,i.jsx)("span",{className:"text-green-600",children:void 0!==D.quickVideoAdvantageRemainingDays?"Active - ".concat(D.quickVideoAdvantageRemainingDays," days remaining"):D.quickVideoAdvantageExpiry?"Active until ".concat(D.quickVideoAdvantageExpiry instanceof Date?D.quickVideoAdvantageExpiry.toLocaleDateString():new Date(D.quickVideoAdvantageExpiry).toLocaleDateString()):"Active"}):(0,i.jsx)("span",{className:"text-gray-500",children:"Not active"})]})})]})]})]}),(0,i.jsx)("div",{className:"p-6 border-t border-gray-200 flex-shrink-0",children:(0,i.jsxs)("div",{className:"flex gap-4",children:[(0,i.jsx)("button",{onClick:O,disabled:S,className:"flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50",children:S?"Saving...":"Save Changes"}),(0,i.jsx)("button",{onClick:()=>E(!1),className:"flex-1 bg-gray-600 text-white py-2 rounded-lg hover:bg-gray-700",children:"Cancel"})]})})]})})]})}},3737:(e,t,a)=>{"use strict";function i(e,t,a){if(!e||0===e.length)return void alert("No data to export");let i=a||Object.keys(e[0]),o=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],n=new Blob(["\uFEFF"+[i.join(","),...e.map(e=>i.map(t=>{let a=e[t];if(null==a)return"";let i=o.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof a){let e=a.replace(/"/g,'""');return'"'.concat(e,'"')}return a instanceof Date?'"'.concat(a.toLocaleDateString(),'"'):"object"==typeof a&&null!==a&&a.toDate?'"'.concat(a.toDate().toLocaleDateString(),'"'):i&&("number"==typeof a||!isNaN(Number(a)))?'"'.concat(a,'"'):"number"==typeof a?a.toString():'"'.concat(String(a),'"')}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),r=document.createElement("a");if(void 0!==r.download){let e=URL.createObjectURL(n);r.setAttribute("href",e),r.setAttribute("download","".concat(t,"_").concat(new Date().toISOString().split("T")[0],".csv")),r.style.visibility="hidden",document.body.appendChild(r),r.click(),document.body.removeChild(r)}}function o(e){return e.map(e=>({"User ID":e.id||"",Name:e.name||"",Email:e.email||"",Mobile:String(e.mobile||""),"Referral Code":e.referralCode||"","Referred By":e.referredBy||"Direct",Plan:e.plan||"","Plan Expiry":e.planExpiry instanceof Date?e.planExpiry.toLocaleDateString():e.planExpiry?new Date(e.planExpiry).toLocaleDateString():"","Active Days":e.activeDays||0,"Total Videos":e.totalVideos||0,"Today Videos":e.todayVideos||0,"Last Video Date":e.lastVideoDate instanceof Date?e.lastVideoDate.toLocaleDateString():e.lastVideoDate?new Date(e.lastVideoDate).toLocaleDateString():"","Video Duration (seconds)":e.videoDuration||300,"Quick Video Advantage":e.quickVideoAdvantage?"Yes":"No","Quick Video Advantage Expiry":e.quickVideoAdvantageExpiry instanceof Date?e.quickVideoAdvantageExpiry.toLocaleDateString():e.quickVideoAdvantageExpiry?new Date(e.quickVideoAdvantageExpiry).toLocaleDateString():"","Quick Video Remaining Days":e.quickVideoAdvantageRemainingDays||0,"Quick Video Advantage Granted By":e.quickVideoAdvantageGrantedBy||"","Wallet Balance":e.wallet||0,"Referral Bonus Credited":e.referralBonusCredited?"Yes":"No",Status:e.status||"","Joined Date":e.joinedDate instanceof Date?e.joinedDate.toLocaleDateString():e.joinedDate?new Date(e.joinedDate).toLocaleDateString():"","Joined Time":e.joinedDate instanceof Date?e.joinedDate.toLocaleTimeString():e.joinedDate?new Date(e.joinedDate).toLocaleTimeString():""}))}function n(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function r(e){return e.map(e=>{var t,a,i,o;return{"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":(null==(t=e.bankDetails)?void 0:t.accountHolderName)||"","Bank Name":(null==(a=e.bankDetails)?void 0:a.bankName)||"","Account Number":String((null==(i=e.bankDetails)?void 0:i.accountNumber)||""),"IFSC Code":(null==(o=e.bankDetails)?void 0:o.ifscCode)||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}})}function s(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}a.d(t,{Bf:()=>i,Fz:()=>o,Pe:()=>s,dB:()=>r,sL:()=>n})},6273:(e,t,a)=>{"use strict";a.d(t,{x8:()=>f});var i=a(2144),o=a(6104);let n=(0,i.Qg)(o.Cn,"getUserDashboardData"),r=(0,i.Qg)(o.Cn,"submitVideoBatch"),s=(0,i.Qg)(o.Cn,"processWithdrawalRequest"),l=(0,i.Qg)(o.Cn,"getUserNotifications"),d=(0,i.Qg)(o.Cn,"getUserTransactions"),c=(0,i.Qg)(o.Cn,"getAdminWithdrawals"),u=(0,i.Qg)(o.Cn,"getAdminDashboardStats"),g=(0,i.Qg)(o.Cn,"getAdminUsers"),m=(0,i.Qg)(o.Cn,"getAdminNotifications"),p=(0,i.Qg)(o.Cn,"createAdminNotification");async function x(e){try{console.log("\uD83D\uDE80 Using optimized dashboard data function for user:",e),console.log("\uD83D\uDD17 Functions instance:",o.Cn.app.options.projectId);let t=await n({userId:e});if(console.log("\uD83D\uDCE1 Function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success){console.log("✅ Dashboard data loaded via optimized function");let t=e.data;return{userData:{name:t.user.name,email:t.user.email,mobile:t.user.mobile,referralCode:t.user.referralCode,plan:t.user.plan,planExpiry:null,activeDays:t.user.activeDays},walletData:{wallet:t.user.wallet},videoData:{totalVideos:t.videos.total,todayVideos:t.videos.today,remainingVideos:t.videos.remaining}}}throw console.error("❌ Function returned success: false",e),Error("Function returned success: false")}throw console.error("❌ Invalid function response structure:",t),Error("Invalid response from dashboard function")}catch(e){throw console.error("❌ Error in optimized dashboard data:",e),console.error("❌ Error details:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),e}}async function h(){try{console.log("\uD83D\uDE80 Using optimized admin dashboard stats function...");let e=await u({});if(e.data&&"object"==typeof e.data&&"success"in e.data){let t=e.data;if(t.success)return console.log("✅ Admin dashboard stats loaded via optimized function"),t.data}throw Error("Invalid response from admin dashboard stats function")}catch(e){throw console.error("❌ Error in optimized admin dashboard stats:",e),e}}let f={getDashboardData:async function(e){try{return await x(e)}catch(l){console.warn("⚠️ Optimized function failed, falling back to direct calls");let{getUserData:t,getWalletData:i,getVideoCountData:o}=await a.e(3592).then(a.bind(a,3592)),[n,r,s]=await Promise.all([t(e),i(e),o(e)]);return{userData:n,walletData:r,videoData:s}}},submitVideoBatch:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;try{console.log("\uD83D\uDE80 Using optimized video batch submission...");let a=await r({userId:e,videoCount:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Video batch submitted via optimized function"),e.data}throw Error("Invalid response from video batch function")}catch(e){throw console.error("❌ Error in optimized video batch submission:",e),e}},processWithdrawal:async function(e){try{console.log("\uD83D\uDE80 Using optimized withdrawal processing...");let t=await s(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Withdrawal processed via optimized function"),e.data}throw Error("Invalid response from withdrawal function")}catch(e){throw console.error("❌ Error in optimized withdrawal processing:",e),e}},getUserNotifications:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{console.log("\uD83D\uDE80 Using optimized notifications function...");let a=await l({userId:e,limit:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Notifications loaded via optimized function"),e.data}throw Error("Invalid response from notifications function")}catch(e){throw console.error("❌ Error in optimized notifications:",e),e}},getUserTransactions:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"all";try{console.log("\uD83D\uDE80 Using optimized transactions function...");let i=await d({userId:e,limit:t,type:a});if(i.data&&"object"==typeof i.data&&"success"in i.data){let e=i.data;if(e.success)return console.log("✅ Transactions loaded via optimized function"),e.data}throw Error("Invalid response from transactions function")}catch(e){throw console.error("❌ Error in optimized transactions:",e),e}},getAdminWithdrawals:async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{console.log("\uD83D\uDE80 Using optimized admin withdrawals function, showAll:",e),console.log("\uD83D\uDD17 Functions instance:",o.Cn.app.options.projectId);let t=await c({showAllWithdrawals:e});if(console.log("\uD83D\uDCE1 Admin withdrawals function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin withdrawals loaded via optimized function"),e.data;throw console.error("❌ Admin withdrawals function returned success: false",e),Error("Admin withdrawals function returned success: false")}throw console.error("❌ Invalid admin withdrawals function response structure:",t),Error("Invalid response from admin withdrawals function")}catch(e){throw console.error("❌ Error in optimized admin withdrawals:",e),console.error("❌ Error details:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),e}},getAdminDashboardStats:async function(){try{return await h()}catch(t){console.warn("⚠️ Optimized admin stats function failed, falling back to direct calls");let{getAdminDashboardStats:e}=await Promise.all([a.e(3592),a.e(6779)]).then(a.bind(a,6779));return await e()}},getAdminUsers:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{console.log("\uD83D\uDE80 Using optimized admin users function...");let t=await g(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin users loaded via optimized function"),e.data}throw Error("Invalid response from admin users function")}catch(e){throw console.error("❌ Error in optimized admin users:",e),e}},getAdminNotifications:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"all";try{console.log("\uD83D\uDE80 Using optimized admin notifications function...");let a=await m({limit:e,type:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Admin notifications loaded via optimized function"),e.data}throw Error("Invalid response from admin notifications function")}catch(e){throw console.error("❌ Error in optimized admin notifications:",e),e}},createAdminNotification:async function(e){try{console.log("\uD83D\uDE80 Using optimized admin notification creation...");let t=await p(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin notification created via optimized function"),e.data}throw Error("Invalid response from admin notification creation function")}catch(e){throw console.error("❌ Error in optimized admin notification creation:",e),e}},areFunctionsAvailable:async function(){try{console.log("\uD83D\uDD0D Testing Firebase Functions connectivity..."),console.log("\uD83D\uDD17 Functions project:",o.Cn.app.options.projectId),console.log("\uD83D\uDD17 Functions region:",o.Cn.region);let e=await n({userId:"test"});return console.log("✅ Functions are available, test response:",e),!0}catch(e){return console.warn("⚠️ Firebase Functions not available, falling back to direct Firestore"),console.error("❌ Functions test error:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),!1}}}},6779:(e,t,a)=>{"use strict";a.d(t,{CF:()=>c,I0:()=>g,TK:()=>h,getAdminDashboardStats:()=>s,getAllPendingWithdrawals:()=>m,getAllWithdrawals:()=>p,hG:()=>f,lo:()=>l,nQ:()=>u,r2:()=>x,updateWithdrawalStatus:()=>y,x5:()=>d});var i=a(5317),o=a(6104),n=a(3592);let r=new Map;async function s(){let e="dashboard-stats",t=function(e){let t=r.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let a=i.Dc.fromDate(t),s=await (0,i.getDocs)((0,i.collection)(o.db,n.COLLECTIONS.users)),l=s.size,d=(0,i.P)((0,i.collection)(o.db,n.COLLECTIONS.users),(0,i._M)(n.FIELD_NAMES.joinedDate,">=",a)),c=(await (0,i.getDocs)(d)).size,u=0,g=0,m=0,p=0;s.forEach(e=>{var a;let i=e.data();u+=i[n.FIELD_NAMES.totalVideos]||0,g+=i[n.FIELD_NAMES.wallet]||0;let o=null==(a=i[n.FIELD_NAMES.lastVideoDate])?void 0:a.toDate();o&&o.toDateString()===t.toDateString()&&(m+=i[n.FIELD_NAMES.todayVideos]||0)});try{let e=(0,i.P)((0,i.collection)(o.db,n.COLLECTIONS.transactions),(0,i._M)(n.FIELD_NAMES.type,"==","video_earning"),(0,i.AB)(1e3));(await (0,i.getDocs)(e)).forEach(e=>{var a;let i=e.data(),o=null==(a=i[n.FIELD_NAMES.date])?void 0:a.toDate();o&&o>=t&&(p+=i[n.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let x=(0,i.P)((0,i.collection)(o.db,n.COLLECTIONS.withdrawals),(0,i._M)("status","==","pending")),h=(await (0,i.getDocs)(x)).size,f=(0,i.P)((0,i.collection)(o.db,n.COLLECTIONS.withdrawals),(0,i._M)("date",">=",a)),y=(await (0,i.getDocs)(f)).size,v={totalUsers:l,totalVideos:u,totalEarnings:g,pendingWithdrawals:h,todayUsers:c,todayVideos:m,todayEarnings:p,todayWithdrawals:y};return r.set(e,{data:v,timestamp:Date.now()}),v}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,i.P)((0,i.collection)(o.db,n.COLLECTIONS.users),(0,i.My)(n.FIELD_NAMES.joinedDate,"desc"),(0,i.AB)(e));t&&(a=(0,i.P)((0,i.collection)(o.db,n.COLLECTIONS.users),(0,i.My)(n.FIELD_NAMES.joinedDate,"desc"),(0,i.HM)(t),(0,i.AB)(e)));let r=await (0,i.getDocs)(a);return{users:r.docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[n.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[n.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}),lastDoc:r.docs[r.docs.length-1]||null,hasMore:r.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function d(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),a=(0,i.P)((0,i.collection)(o.db,n.COLLECTIONS.users),(0,i.My)(n.FIELD_NAMES.joinedDate,"desc"));return(await (0,i.getDocs)(a)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[n.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[n.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}).filter(e=>{let a=String(e[n.FIELD_NAMES.name]||"").toLowerCase(),i=String(e[n.FIELD_NAMES.email]||"").toLowerCase(),o=String(e[n.FIELD_NAMES.mobile]||"").toLowerCase(),r=String(e[n.FIELD_NAMES.referralCode]||"").toLowerCase();return a.includes(t)||i.includes(t)||o.includes(t)||r.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function c(){try{let e=(0,i.P)((0,i.collection)(o.db,n.COLLECTIONS.users),(0,i.My)(n.FIELD_NAMES.joinedDate,"desc"));return(await (0,i.getDocs)(e)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[n.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[n.FIELD_NAMES.planExpiry])?void 0:a.toDate()}})}catch(e){throw console.error("Error getting all users:",e),e}}async function u(){try{let e=(0,i.P)((0,i.collection)(o.db,n.COLLECTIONS.users));return(await (0,i.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function g(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,i.P)((0,i.collection)(o.db,n.COLLECTIONS.transactions),(0,i.My)(n.FIELD_NAMES.date,"desc"),(0,i.AB)(e));t&&(a=(0,i.P)((0,i.collection)(o.db,n.COLLECTIONS.transactions),(0,i.My)(n.FIELD_NAMES.date,"desc"),(0,i.HM)(t),(0,i.AB)(e)));let r=await (0,i.getDocs)(a);return{transactions:r.docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data()[n.FIELD_NAMES.date])?void 0:t.toDate()}}),lastDoc:r.docs[r.docs.length-1]||null,hasMore:r.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function m(){try{console.log("\uD83D\uDD0D Loading ALL pending withdrawals...");let e=(0,i.P)((0,i.collection)(o.db,n.COLLECTIONS.withdrawals),(0,i._M)("status","==","pending"),(0,i.My)("date","desc")),t=(await (0,i.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," pending withdrawals")),t}catch(e){throw console.error("Error getting all pending withdrawals:",e),e}}async function p(){try{console.log("\uD83D\uDD0D Loading ALL withdrawals...");let e=(0,i.P)((0,i.collection)(o.db,n.COLLECTIONS.withdrawals),(0,i.My)("date","desc")),t=(await (0,i.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," total withdrawals")),t}catch(e){throw console.error("Error getting all withdrawals:",e),e}}async function x(){try{console.log("\uD83D\uDD0D Loading ALL transactions...");let e=(0,i.P)((0,i.collection)(o.db,n.COLLECTIONS.transactions),(0,i.My)(n.FIELD_NAMES.date,"desc")),t=(await (0,i.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data()[n.FIELD_NAMES.date])?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," total transactions")),t}catch(e){throw console.error("Error getting all transactions:",e),e}}async function h(e,t){try{await (0,i.mZ)((0,i.H9)(o.db,n.COLLECTIONS.users,e),t),r.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function f(e){try{await (0,i.kd)((0,i.H9)(o.db,n.COLLECTIONS.users,e)),r.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function y(e,t,s){try{let l=await (0,i.x7)((0,i.H9)(o.db,n.COLLECTIONS.withdrawals,e));if(!l.exists())throw Error("Withdrawal not found");let{userId:d,amount:c,status:u}=l.data(),g={status:t,updatedAt:i.Dc.now()};if(s&&(g.adminNotes=s),await (0,i.mZ)((0,i.H9)(o.db,n.COLLECTIONS.withdrawals,e),g),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(a.bind(a,3592));await e(d,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(c," processed for transfer")})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(a.bind(a,3592));await e(d,c),await t(d,{type:"withdrawal_rejected",amount:c,description:"Withdrawal rejected - ₹".concat(c," credited back to wallet")})}r.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},7428:(e,t,a)=>{Promise.resolve().then(a.bind(a,2899))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6874,3592,6681,8441,1684,7358],()=>t(7428)),_N_E=e.O()}]);