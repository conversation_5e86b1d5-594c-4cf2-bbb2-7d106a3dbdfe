{"/_not-found/page": "/_not-found", "/admin/fix-plan-expiry/page": "/admin/fix-plan-expiry", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/login/page": "/admin/login", "/admin/leaves/page": "/admin/leaves", "/admin/notifications/page": "/admin/notifications", "/admin/page": "/admin", "/admin/settings/page": "/admin/settings", "/admin/setup/page": "/admin/setup", "/admin/reset-daily-tracking/page": "/admin/reset-daily-tracking", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/upload-users/page": "/admin/upload-users", "/admin/transactions/page": "/admin/transactions", "/dashboard/page": "/dashboard", "/admin/users/page": "/admin/users", "/debug-active-days/page": "/debug-active-days", "/clear-cache/page": "/clear-cache", "/debug-dates/page": "/debug-dates", "/admin/withdrawals/page": "/admin/withdrawals", "/debug-firestore/page": "/debug-firestore", "/debug-firestore-issue/page": "/debug-firestore-issue", "/forgot-password/page": "/forgot-password", "/debug-registration/page": "/debug-registration", "/login/page": "/login", "/refer/page": "/refer", "/debug-registration-simple/page": "/debug-registration-simple", "/plans/page": "/plans", "/register/page": "/register", "/page": "/", "/profile/page": "/profile", "/reset-password/page": "/reset-password", "/support/page": "/support", "/registration-diagnostics/page": "/registration-diagnostics", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firestore/page": "/test-firestore", "/test-firebase/page": "/test-firebase", "/test-functions/page": "/test-functions", "/test-reg-simple/page": "/test-reg-simple", "/test-registration/page": "/test-registration", "/test-videos/page": "/test-videos", "/test-simple-registration/page": "/test-simple-registration", "/transactions/page": "/transactions", "/wallet/page": "/wallet", "/work/page": "/work"}