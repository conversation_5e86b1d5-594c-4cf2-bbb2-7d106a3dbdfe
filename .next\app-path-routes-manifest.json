{"/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/leaves/page": "/admin/leaves", "/admin/fix-plan-expiry/page": "/admin/fix-plan-expiry", "/admin/login/page": "/admin/login", "/admin/page": "/admin", "/admin/reset-daily-tracking/page": "/admin/reset-daily-tracking", "/admin/settings/page": "/admin/settings", "/admin/transactions/page": "/admin/transactions", "/admin/notifications/page": "/admin/notifications", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/setup/page": "/admin/setup", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/users/page": "/admin/users", "/admin/upload-users/page": "/admin/upload-users", "/admin/withdrawals/page": "/admin/withdrawals", "/debug-active-days/page": "/debug-active-days", "/clear-cache/page": "/clear-cache", "/debug-dates/page": "/debug-dates", "/debug-firestore-issue/page": "/debug-firestore-issue", "/debug-firestore/page": "/debug-firestore", "/debug-registration-simple/page": "/debug-registration-simple", "/login/page": "/login", "/plans/page": "/plans", "/page": "/", "/profile/page": "/profile", "/debug-registration/page": "/debug-registration", "/dashboard/page": "/dashboard", "/register/page": "/register", "/forgot-password/page": "/forgot-password", "/admin/fix-active-days/page": "/admin/fix-active-days", "/refer/page": "/refer", "/reset-password/page": "/reset-password", "/_not-found/page": "/_not-found", "/registration-diagnostics/page": "/registration-diagnostics", "/support/page": "/support", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase/page": "/test-firebase", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firestore/page": "/test-firestore", "/test-functions/page": "/test-functions", "/test-reg-simple/page": "/test-reg-simple", "/test-simple-registration/page": "/test-simple-registration", "/test-registration/page": "/test-registration", "/test-videos/page": "/test-videos", "/transactions/page": "/transactions", "/work/page": "/work", "/wallet/page": "/wallet"}