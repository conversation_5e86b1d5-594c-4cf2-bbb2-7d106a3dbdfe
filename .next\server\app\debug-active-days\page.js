(()=>{var e={};e.id=8925,e.ids=[1391,3772,8925],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3921:(e,t,s)=>{Promise.resolve().then(s.bind(s,59430))},4573:e=>{"use strict";e.exports=require("node:buffer")},4593:(e,t,s)=>{Promise.resolve().then(s.bind(s,85319))},7127:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=s(65239),r=s(48088),i=s(88170),o=s.n(i),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let c={children:["",{children:["debug-active-days",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,85319)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\debug-active-days\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\debug-active-days\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/debug-active-days/page",pathname:"/debug-active-days",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},59430:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var a=s(60687),r=s(43210),i=s(87979),o=s(91391),n=s(3582),l=s(92617),c=s(33784),d=s(75535);async function u(){let e={analyzed:0,needsRestoration:[],restored:0,errors:[]};try{console.log("\uD83D\uDD0D Starting active days restoration analysis...");let t=await (0,d.getDocs)((0,d.collection)(c.db,n.COLLECTIONS.users)),s=new Date;for(let a of(s.setHours(0,0,0,0),t.docs))try{let t=a.data(),r=a.id;if(e.analyzed++,!t[n.FIELD_NAMES.joinedDate])continue;let i=t[n.FIELD_NAMES.joinedDate].toDate();i.setHours(0,0,0,0);let o=t[n.FIELD_NAMES.activeDays]||1,l=t[n.FIELD_NAMES.plan]||"Trial",c=t[n.FIELD_NAMES.manuallySetActiveDays]||!1,d=Math.floor((s.getTime()-i.getTime())/864e5),u=1,p=!1,m="";if(c)m="Manually set by admin - skipping";else if("Trial"===l){let e=Math.min(d+1,10);o>e&&(u=Math.max(1,Math.min(d+1,3)),p=!0,m=`Trial user with ${o} active days (joined ${d} days ago)`)}else{let e=d+5;o>e&&(u=Math.max(1,Math.min(d+1,30)),p=!0,m=`Paid user with ${o} active days (joined ${d} days ago)`)}p&&e.needsRestoration.push({userId:r,email:t[n.FIELD_NAMES.email]||"Unknown",plan:l,currentActiveDays:o,joinedDate:i,daysSinceJoined:d,suggestedActiveDays:u,shouldRestore:p,reason:m})}catch(t){e.errors.push(`Error processing user ${a.id}: ${t}`)}return console.log(`✅ Analysis complete. Found ${e.needsRestoration.length} users needing restoration out of ${e.analyzed} total users.`),e}catch(t){return console.error("Error in active days restoration analysis:",t),e.errors.push(`Analysis error: ${t}`),e}}async function p(e){let t={restored:0,errors:[]};try{console.log(`🔧 Starting restoration for ${e.length} users...`);let s=(0,d.wP)(c.db),a=0;for(let r of e)try{let e=(0,d.H9)(c.db,n.COLLECTIONS.users,r.userId);s.update(e,{[n.FIELD_NAMES.activeDays]:r.suggestedActiveDays,[n.FIELD_NAMES.manuallySetActiveDays]:!1,[n.FIELD_NAMES.lastActiveDaysUpdate]:new Date}),a++,t.restored++,a>=500&&(await s.commit(),s=(0,d.wP)(c.db),a=0,console.log(`📦 Committed batch of 500 updates`))}catch(e){t.errors.push(`Error restoring user ${r.email}: ${e}`)}return a>0&&(await s.commit(),console.log(`📦 Committed final batch of ${a} updates`)),console.log(`✅ Restoration complete. Restored ${t.restored} users.`),t}catch(e){return console.error("Error in active days restoration:",e),t.errors.push(`Restoration error: ${e}`),t}}async function m(){try{console.log("\uD83D\uDCBE Creating active days backup...");let e=await (0,d.getDocs)((0,d.collection)(c.db,n.COLLECTIONS.users)),t=[];for(let s of e.docs){let e=s.data();t.push({userId:s.id,email:e[n.FIELD_NAMES.email],activeDays:e[n.FIELD_NAMES.activeDays],manuallySetActiveDays:e[n.FIELD_NAMES.manuallySetActiveDays],lastActiveDaysUpdate:e[n.FIELD_NAMES.lastActiveDaysUpdate],plan:e[n.FIELD_NAMES.plan],joinedDate:e[n.FIELD_NAMES.joinedDate],backupTimestamp:new Date})}return console.log(`✅ Backup created for ${t.length} users`),{success:!0,backupData:t}}catch(e){return console.error("Error creating backup:",e),{success:!1,backupData:[],error:String(e)}}}var h=s(77567);function g(){let{isAdmin:e,loading:t}=(0,i.wC)(),[s,c]=(0,r.useState)([]),[d,g]=(0,r.useState)(!1),[y,x]=(0,r.useState)(!1),[f,w]=(0,r.useState)(!1),[v,b]=(0,r.useState)(null),[D,E]=(0,r.useState)(!1),[A,N]=(0,r.useState)(null),j=async()=>{try{g(!0),c([]),console.log("\uD83D\uDD0D Starting active days debug check...");let e=(await (0,o.CF)()).slice(0,10),t=[];for(let s of e)try{let e=s.activeDays||0,a=await (0,n.calculateUserActiveDays)(s.id),r=e!==a;t.push({userId:s.id,email:s.email,plan:s.plan,joinedDate:s.joinedDate,planExpiry:s.planExpiry,storedActiveDays:e,calculatedActiveDays:a,discrepancy:r,manuallySet:s.manuallySetActiveDays||!1,lastUpdate:s.lastActiveDaysUpdate}),console.log(`User ${s.email}: Stored=${e}, Calculated=${a}, Discrepancy=${r}`)}catch(e){console.error(`Error processing user ${s.id}:`,e),t.push({userId:s.id,email:s.email,error:e.message})}c(t),console.log("✅ Debug check completed")}catch(e){console.error("Error in debug check:",e)}finally{g(!1)}},S=async()=>{try{x(!0),console.log("\uD83E\uDDEA Testing Firebase function after deployment...");let e=await l.x8.getDashboardData("current-user");if(e.userData)h.A.fire({icon:"success",title:"Firebase Function Test Successful!",html:`
            <div class="text-left">
              <p><strong>User Active Days:</strong> ${e.userData.activeDays}</p>
              <p><strong>Plan:</strong> ${e.userData.plan}</p>
              <p><strong>Status:</strong> Firebase function is now using stored database values</p>
            </div>
          `,timer:5e3,showConfirmButton:!0});else throw Error("Function test failed")}catch(e){console.error("Error testing Firebase function:",e),h.A.fire({icon:"error",title:"Firebase Function Test Failed",text:"There was an error testing the deployed function."})}finally{x(!1)}},C=async()=>{try{w(!0),b(null),console.log("\uD83D\uDD0D Analyzing active days issue across all users...");let e=await (0,o.CF)(),t={totalUsers:e.length,trialUsers:0,paidUsers:0,suspiciouslyHighActiveDays:[],normalActiveDays:[],manuallySetUsers:[],recentlyJoinedButHighActiveDays:[]},s=new Date;for(let a of e){let e=a.activeDays||0,r=a.plan||"Unknown",i=a.joinedDate?new Date(1e3*a.joinedDate.seconds):null,o=a.manuallySetActiveDays||!1;if("Trial"===r?t.trialUsers++:t.paidUsers++,o&&t.manuallySetUsers.push({email:a.email,plan:r,activeDays:e,joinedDate:i}),i){let o=Math.floor((s.getTime()-i.getTime())/864e5);e>o+5?t.suspiciouslyHighActiveDays.push({email:a.email,plan:r,activeDays:e,daysSinceJoined:o,joinedDate:i,difference:e-o}):t.normalActiveDays.push({email:a.email,plan:r,activeDays:e,daysSinceJoined:o}),o<=10&&e>15&&t.recentlyJoinedButHighActiveDays.push({email:a.email,plan:r,activeDays:e,daysSinceJoined:o,joinedDate:i})}}b(t),console.log("✅ Analysis completed:",t),h.A.fire({icon:"info",title:"Active Days Analysis Complete",html:`
          <div class="text-left">
            <p><strong>Total Users:</strong> ${t.totalUsers}</p>
            <p><strong>Trial Users:</strong> ${t.trialUsers}</p>
            <p><strong>Paid Users:</strong> ${t.paidUsers}</p>
            <p><strong>Suspicious High Active Days:</strong> ${t.suspiciouslyHighActiveDays.length}</p>
            <p><strong>Normal Active Days:</strong> ${t.normalActiveDays.length}</p>
            <p><strong>Manually Set by Admin:</strong> ${t.manuallySetUsers.length}</p>
          </div>
        `,showConfirmButton:!0})}catch(e){console.error("Error analyzing active days:",e),h.A.fire({icon:"error",title:"Analysis Failed",text:"There was an error analyzing the active days data."})}finally{w(!1)}},L=async()=>{try{if(!(await h.A.fire({icon:"warning",title:"Restore Active Days?",html:`
          <div class="text-left">
            <p><strong>⚠️ This will:</strong></p>
            <ul class="list-disc list-inside mt-2 space-y-1">
              <li>Analyze all users for incorrect active days</li>
              <li>Create a backup of current values</li>
              <li>Restore reasonable active days based on join dates</li>
              <li>Reset manual override flags</li>
            </ul>
            <p class="mt-4 text-red-600"><strong>This action cannot be easily undone!</strong></p>
          </div>
        `,showCancelButton:!0,confirmButtonText:"Yes, Restore Active Days",cancelButtonText:"Cancel",confirmButtonColor:"#dc2626"})).isConfirmed)return;E(!0),console.log("\uD83D\uDD27 Starting active days restoration process..."),h.A.fire({title:"Creating Backup...",text:"Please wait while we backup current active days data.",allowOutsideClick:!1,didOpen:()=>{h.A.showLoading()}});let e=await m();if(!e.success)throw Error(`Backup failed: ${e.error}`);h.A.update({title:"Analyzing Users...",text:"Analyzing all users to identify incorrect active days."});let t=await u();if(0===t.needsRestoration.length)return void h.A.fire({icon:"success",title:"No Issues Found!",text:"All users have reasonable active days values. No restoration needed."});if(!(await h.A.fire({icon:"question",title:`Found ${t.needsRestoration.length} Users to Restore`,html:`
          <div class="text-left">
            <p><strong>Users needing restoration:</strong> ${t.needsRestoration.length}</p>
            <p><strong>Total users analyzed:</strong> ${t.analyzed}</p>
            <div class="mt-4 max-h-40 overflow-y-auto bg-gray-50 p-3 rounded">
              ${t.needsRestoration.slice(0,10).map(e=>`<div class="text-sm mb-1">
                  <strong>${e.email}</strong> (${e.plan}): ${e.currentActiveDays} → ${e.suggestedActiveDays} days
                </div>`).join("")}
              ${t.needsRestoration.length>10?`<div class="text-sm text-gray-600">... and ${t.needsRestoration.length-10} more</div>`:""}
            </div>
          </div>
        `,showCancelButton:!0,confirmButtonText:"Proceed with Restoration",cancelButtonText:"Cancel"})).isConfirmed)return;h.A.fire({title:"Restoring Active Days...",text:`Updating ${t.needsRestoration.length} users...`,allowOutsideClick:!1,didOpen:()=>{h.A.showLoading()}});let s=await p(t.needsRestoration);N({backup:e,analysis:t,restoration:s}),h.A.fire({icon:"success",title:"Active Days Restoration Complete!",html:`
          <div class="text-left">
            <p><strong>✅ Backup Created:</strong> ${e.backupData.length} users</p>
            <p><strong>🔍 Users Analyzed:</strong> ${t.analyzed}</p>
            <p><strong>🔧 Users Restored:</strong> ${s.restored}</p>
            <p><strong>❌ Errors:</strong> ${s.errors.length}</p>
            ${s.errors.length>0?`
              <div class="mt-2 text-red-600 text-sm">
                <strong>Errors:</strong><br>
                ${s.errors.slice(0,3).join("<br>")}
                ${s.errors.length>3?`<br>... and ${s.errors.length-3} more`:""}
              </div>
            `:""}
          </div>
        `,timer:1e4,showConfirmButton:!0}),console.log("✅ Active days restoration completed successfully")}catch(e){console.error("Error in active days restoration:",e),h.A.fire({icon:"error",title:"Restoration Failed",text:`There was an error during restoration: ${e}`})}finally{E(!1)}};return t?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):e?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Debug Active Days"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Compare stored Firestore values vs calculated values for active days"})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex space-x-4 mb-6",children:[(0,a.jsx)("button",{onClick:j,disabled:d,className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50",children:d?"Processing...":"Run Debug Check (First 10 Users)"}),(0,a.jsx)("button",{onClick:S,disabled:y,className:"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50",children:y?"Testing...":"Test Fixed Firebase Function"}),(0,a.jsx)("button",{onClick:C,disabled:f,className:"bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 disabled:opacity-50",children:f?"Analyzing...":"Analyze Active Days Issue"}),(0,a.jsx)("button",{onClick:L,disabled:D,className:"bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 disabled:opacity-50",children:D?"Restoring...":"\uD83D\uDD27 Restore Active Days"})]}),(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"fas fa-info-circle text-yellow-600 mt-1"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"Active Days Fix Status"}),(0,a.jsxs)("div",{className:"mt-2 text-sm text-yellow-700",children:[(0,a.jsxs)("p",{children:["✅ ",(0,a.jsx)("strong",{children:"Firebase Function Fixed:"})," Now uses stored database values instead of recalculating"]}),(0,a.jsxs)("p",{children:["\uD83D\uDCCA ",(0,a.jsx)("strong",{children:"Database Values:"})," The stored activeDays in Firestore are correct"]}),(0,a.jsxs)("p",{children:["\uD83D\uDD27 ",(0,a.jsx)("strong",{children:"What Changed:"})," Withdrawals page and admin functions now show accurate active days"]})]})]})]})}),v&&(0,a.jsxs)("div",{className:"mt-8",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Active Days Issue Analysis"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-medium text-blue-900",children:"Total Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:v.totalUsers})]}),(0,a.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-medium text-green-900",children:"Normal Active Days"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:v.normalActiveDays.length})]}),(0,a.jsxs)("div",{className:"bg-red-50 p-4 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-medium text-red-900",children:"Suspicious High"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-red-600",children:v.suspiciouslyHighActiveDays.length})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-medium text-yellow-900",children:"Manually Set"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:v.manuallySetUsers.length})]})]}),v.suspiciouslyHighActiveDays.length>0&&(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:[(0,a.jsx)("h3",{className:"font-medium text-red-900 mb-3",children:"\uD83D\uDEA8 Users with Suspiciously High Active Days"}),(0,a.jsxs)("div",{className:"max-h-60 overflow-y-auto",children:[(0,a.jsxs)("table",{className:"min-w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"text-left text-xs font-medium text-red-700 uppercase",children:"Email"}),(0,a.jsx)("th",{className:"text-left text-xs font-medium text-red-700 uppercase",children:"Plan"}),(0,a.jsx)("th",{className:"text-left text-xs font-medium text-red-700 uppercase",children:"Active Days"}),(0,a.jsx)("th",{className:"text-left text-xs font-medium text-red-700 uppercase",children:"Days Since Joined"}),(0,a.jsx)("th",{className:"text-left text-xs font-medium text-red-700 uppercase",children:"Difference"})]})}),(0,a.jsx)("tbody",{children:v.suspiciouslyHighActiveDays.slice(0,10).map((e,t)=>(0,a.jsxs)("tr",{className:"text-sm",children:[(0,a.jsx)("td",{className:"py-1 text-red-900",children:e.email}),(0,a.jsx)("td",{className:"py-1 text-red-900",children:e.plan}),(0,a.jsx)("td",{className:"py-1 font-bold text-red-600",children:e.activeDays}),(0,a.jsx)("td",{className:"py-1 text-red-900",children:e.daysSinceJoined}),(0,a.jsxs)("td",{className:"py-1 font-bold text-red-600",children:["+",e.difference]})]},t))})]}),v.suspiciouslyHighActiveDays.length>10&&(0,a.jsxs)("p",{className:"text-sm text-red-600 mt-2",children:["... and ",v.suspiciouslyHighActiveDays.length-10," more users"]})]})]})]}),s.length>0&&(0,a.jsxs)("div",{className:"mt-8",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Debug Results"}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plan"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stored Active Days"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Calculated Active Days"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Discrepancy"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Manually Set"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map((e,t)=>(0,a.jsxs)("tr",{className:e.discrepancy?"bg-red-50":"",children:[(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.email}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.userId})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.plan}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.storedActiveDays}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.calculatedActiveDays}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.discrepancy?(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800",children:"YES"}):(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:"NO"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.manuallySet?"Yes":"No"})]},t))})]})})]})]})]})})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-600",children:"You don't have permission to access this page."})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85319:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-active-days\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\debug-active-days\\page.tsx","default")},91391:(e,t,s)=>{"use strict";s.d(t,{CF:()=>d,I0:()=>p,TK:()=>y,getAdminDashboardStats:()=>n,getAllPendingWithdrawals:()=>m,getAllWithdrawals:()=>h,hG:()=>x,lo:()=>l,nQ:()=>u,r2:()=>g,updateWithdrawalStatus:()=>f,x5:()=>c});var a=s(75535),r=s(33784),i=s(3582);let o=new Map;async function n(){let e="dashboard-stats",t=function(e){let t=o.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let s=a.Dc.fromDate(t),n=await (0,a.getDocs)((0,a.collection)(r.db,i.COLLECTIONS.users)),l=n.size,c=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users),(0,a._M)(i.FIELD_NAMES.joinedDate,">=",s)),d=(await (0,a.getDocs)(c)).size,u=0,p=0,m=0,h=0;n.forEach(e=>{let s=e.data();u+=s[i.FIELD_NAMES.totalVideos]||0,p+=s[i.FIELD_NAMES.wallet]||0;let a=s[i.FIELD_NAMES.lastVideoDate]?.toDate();a&&a.toDateString()===t.toDateString()&&(m+=s[i.FIELD_NAMES.todayVideos]||0)});try{let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.transactions),(0,a._M)(i.FIELD_NAMES.type,"==","video_earning"),(0,a.AB)(1e3));(await (0,a.getDocs)(e)).forEach(e=>{let s=e.data(),a=s[i.FIELD_NAMES.date]?.toDate();a&&a>=t&&(h+=s[i.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let g=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.withdrawals),(0,a._M)("status","==","pending")),y=(await (0,a.getDocs)(g)).size,x=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.withdrawals),(0,a._M)("date",">=",s)),f=(await (0,a.getDocs)(x)).size,w={totalUsers:l,totalVideos:u,totalEarnings:p,pendingWithdrawals:y,todayUsers:d,todayVideos:m,todayEarnings:h,todayWithdrawals:f};return o.set(e,{data:w,timestamp:Date.now()}),w}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function l(e=50,t=null){try{let s=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users),(0,a.My)(i.FIELD_NAMES.joinedDate,"desc"),(0,a.AB)(e));t&&(s=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users),(0,a.My)(i.FIELD_NAMES.joinedDate,"desc"),(0,a.HM)(t),(0,a.AB)(e)));let o=await (0,a.getDocs)(s);return{users:o.docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()})),lastDoc:o.docs[o.docs.length-1]||null,hasMore:o.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function c(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),s=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users),(0,a.My)(i.FIELD_NAMES.joinedDate,"desc"));return(await (0,a.getDocs)(s)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()})).filter(e=>{let s=String(e[i.FIELD_NAMES.name]||"").toLowerCase(),a=String(e[i.FIELD_NAMES.email]||"").toLowerCase(),r=String(e[i.FIELD_NAMES.mobile]||"").toLowerCase(),o=String(e[i.FIELD_NAMES.referralCode]||"").toLowerCase();return s.includes(t)||a.includes(t)||r.includes(t)||o.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function d(){try{let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users),(0,a.My)(i.FIELD_NAMES.joinedDate,"desc"));return(await (0,a.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()}))}catch(e){throw console.error("Error getting all users:",e),e}}async function u(){try{let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users));return(await (0,a.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function p(e=50,t=null){try{let s=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.transactions),(0,a.My)(i.FIELD_NAMES.date,"desc"),(0,a.AB)(e));t&&(s=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.transactions),(0,a.My)(i.FIELD_NAMES.date,"desc"),(0,a.HM)(t),(0,a.AB)(e)));let o=await (0,a.getDocs)(s);return{transactions:o.docs.map(e=>({id:e.id,...e.data(),date:e.data()[i.FIELD_NAMES.date]?.toDate()})),lastDoc:o.docs[o.docs.length-1]||null,hasMore:o.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function m(){try{console.log("\uD83D\uDD0D Loading ALL pending withdrawals...");let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.withdrawals),(0,a._M)("status","==","pending"),(0,a.My)("date","desc")),t=(await (0,a.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()}));return console.log(`✅ Loaded ${t.length} pending withdrawals`),t}catch(e){throw console.error("Error getting all pending withdrawals:",e),e}}async function h(){try{console.log("\uD83D\uDD0D Loading ALL withdrawals...");let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.withdrawals),(0,a.My)("date","desc")),t=(await (0,a.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()}));return console.log(`✅ Loaded ${t.length} total withdrawals`),t}catch(e){throw console.error("Error getting all withdrawals:",e),e}}async function g(){try{console.log("\uD83D\uDD0D Loading ALL transactions...");let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.transactions),(0,a.My)(i.FIELD_NAMES.date,"desc")),t=(await (0,a.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data()[i.FIELD_NAMES.date]?.toDate()}));return console.log(`✅ Loaded ${t.length} total transactions`),t}catch(e){throw console.error("Error getting all transactions:",e),e}}async function y(e,t){try{await (0,a.mZ)((0,a.H9)(r.db,i.COLLECTIONS.users,e),t),o.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function x(e){try{await (0,a.kd)((0,a.H9)(r.db,i.COLLECTIONS.users,e)),o.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function f(e,t,n){try{let l=await (0,a.x7)((0,a.H9)(r.db,i.COLLECTIONS.withdrawals,e));if(!l.exists())throw Error("Withdrawal not found");let{userId:c,amount:d,status:u}=l.data(),p={status:t,updatedAt:a.Dc.now()};if(n&&(p.adminNotes=n),await (0,a.mZ)((0,a.H9)(r.db,i.COLLECTIONS.withdrawals,e),p),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(s.bind(s,3582));await e(c,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${d} processed for transfer`})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(s.bind(s,3582));await e(c,d),await t(c,{type:"withdrawal_rejected",amount:d,description:`Withdrawal rejected - ₹${d} credited back to wallet`})}o.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},91645:e=>{"use strict";e.exports=require("net")},92617:(e,t,s)=>{"use strict";s.d(t,{x8:()=>x});var a=s(24791),r=s(33784);let i=(0,a.Qg)(r.Cn,"getUserDashboardData"),o=(0,a.Qg)(r.Cn,"submitVideoBatch"),n=(0,a.Qg)(r.Cn,"processWithdrawalRequest"),l=(0,a.Qg)(r.Cn,"getUserNotifications"),c=(0,a.Qg)(r.Cn,"getUserTransactions"),d=(0,a.Qg)(r.Cn,"getAdminWithdrawals"),u=(0,a.Qg)(r.Cn,"getAdminDashboardStats"),p=(0,a.Qg)(r.Cn,"getAdminUsers"),m=(0,a.Qg)(r.Cn,"getAdminNotifications"),h=(0,a.Qg)(r.Cn,"createAdminNotification");async function g(e){try{console.log("\uD83D\uDE80 Using optimized dashboard data function for user:",e),console.log("\uD83D\uDD17 Functions instance:",r.Cn.app.options.projectId);let t=await i({userId:e});if(console.log("\uD83D\uDCE1 Function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success){console.log("✅ Dashboard data loaded via optimized function");let t=e.data;return{userData:{name:t.user.name,email:t.user.email,mobile:t.user.mobile,referralCode:t.user.referralCode,plan:t.user.plan,planExpiry:null,activeDays:t.user.activeDays},walletData:{wallet:t.user.wallet},videoData:{totalVideos:t.videos.total,todayVideos:t.videos.today,remainingVideos:t.videos.remaining}}}throw console.error("❌ Function returned success: false",e),Error("Function returned success: false")}throw console.error("❌ Invalid function response structure:",t),Error("Invalid response from dashboard function")}catch(e){throw console.error("❌ Error in optimized dashboard data:",e),console.error("❌ Error details:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),e}}async function y(){try{console.log("\uD83D\uDE80 Using optimized admin dashboard stats function...");let e=await u({});if(e.data&&"object"==typeof e.data&&"success"in e.data){let t=e.data;if(t.success)return console.log("✅ Admin dashboard stats loaded via optimized function"),t.data}throw Error("Invalid response from admin dashboard stats function")}catch(e){throw console.error("❌ Error in optimized admin dashboard stats:",e),e}}let x={getDashboardData:async function(e){try{return await g(e)}catch(l){console.warn("⚠️ Optimized function failed, falling back to direct calls");let{getUserData:t,getWalletData:a,getVideoCountData:r}=await s.e(3582).then(s.bind(s,3582)),[i,o,n]=await Promise.all([t(e),a(e),r(e)]);return{userData:i,walletData:o,videoData:n}}},submitVideoBatch:async function(e,t=50){try{console.log("\uD83D\uDE80 Using optimized video batch submission...");let s=await o({userId:e,videoCount:t});if(s.data&&"object"==typeof s.data&&"success"in s.data){let e=s.data;if(e.success)return console.log("✅ Video batch submitted via optimized function"),e.data}throw Error("Invalid response from video batch function")}catch(e){throw console.error("❌ Error in optimized video batch submission:",e),e}},processWithdrawal:async function(e){try{console.log("\uD83D\uDE80 Using optimized withdrawal processing...");let t=await n(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Withdrawal processed via optimized function"),e.data}throw Error("Invalid response from withdrawal function")}catch(e){throw console.error("❌ Error in optimized withdrawal processing:",e),e}},getUserNotifications:async function(e,t=10){try{console.log("\uD83D\uDE80 Using optimized notifications function...");let s=await l({userId:e,limit:t});if(s.data&&"object"==typeof s.data&&"success"in s.data){let e=s.data;if(e.success)return console.log("✅ Notifications loaded via optimized function"),e.data}throw Error("Invalid response from notifications function")}catch(e){throw console.error("❌ Error in optimized notifications:",e),e}},getUserTransactions:async function(e,t=10,s="all"){try{console.log("\uD83D\uDE80 Using optimized transactions function...");let a=await c({userId:e,limit:t,type:s});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Transactions loaded via optimized function"),e.data}throw Error("Invalid response from transactions function")}catch(e){throw console.error("❌ Error in optimized transactions:",e),e}},getAdminWithdrawals:async function(e=!1){try{console.log("\uD83D\uDE80 Using optimized admin withdrawals function, showAll:",e),console.log("\uD83D\uDD17 Functions instance:",r.Cn.app.options.projectId);let t=await d({showAllWithdrawals:e});if(console.log("\uD83D\uDCE1 Admin withdrawals function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin withdrawals loaded via optimized function"),e.data;throw console.error("❌ Admin withdrawals function returned success: false",e),Error("Admin withdrawals function returned success: false")}throw console.error("❌ Invalid admin withdrawals function response structure:",t),Error("Invalid response from admin withdrawals function")}catch(e){throw console.error("❌ Error in optimized admin withdrawals:",e),console.error("❌ Error details:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),e}},getAdminDashboardStats:async function(){try{return await y()}catch(t){console.warn("⚠️ Optimized admin stats function failed, falling back to direct calls");let{getAdminDashboardStats:e}=await Promise.all([s.e(3582),s.e(1391)]).then(s.bind(s,91391));return await e()}},getAdminUsers:async function(e={}){try{console.log("\uD83D\uDE80 Using optimized admin users function...");let t=await p(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin users loaded via optimized function"),e.data}throw Error("Invalid response from admin users function")}catch(e){throw console.error("❌ Error in optimized admin users:",e),e}},getAdminNotifications:async function(e=50,t="all"){try{console.log("\uD83D\uDE80 Using optimized admin notifications function...");let s=await m({limit:e,type:t});if(s.data&&"object"==typeof s.data&&"success"in s.data){let e=s.data;if(e.success)return console.log("✅ Admin notifications loaded via optimized function"),e.data}throw Error("Invalid response from admin notifications function")}catch(e){throw console.error("❌ Error in optimized admin notifications:",e),e}},createAdminNotification:async function(e){try{console.log("\uD83D\uDE80 Using optimized admin notification creation...");let t=await h(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin notification created via optimized function"),e.data}throw Error("Invalid response from admin notification creation function")}catch(e){throw console.error("❌ Error in optimized admin notification creation:",e),e}},areFunctionsAvailable:async function(){try{console.log("\uD83D\uDD0D Testing Firebase Functions connectivity..."),console.log("\uD83D\uDD17 Functions project:",r.Cn.app.options.projectId),console.log("\uD83D\uDD17 Functions region:",r.Cn.region);let e=await i({userId:"test"});return console.log("✅ Functions are available, test response:",e),!0}catch(e){return console.warn("⚠️ Firebase Functions not available, falling back to direct Firestore"),console.error("❌ Functions test error:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),!1}}}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[6204,6958,7567,8441,3582,7979],()=>s(7127));module.exports=a})();