(()=>{var e={};e.id=8925,e.ids=[1391,3772,8925],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3921:(e,t,r)=>{Promise.resolve().then(r.bind(r,89881))},4573:e=>{"use strict";e.exports=require("node:buffer")},4593:(e,t,r)=>{Promise.resolve().then(r.bind(r,85319))},7127:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c});var s=r(65239),a=r(48088),o=r(88170),i=r.n(o),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let c={children:["",{children:["debug-active-days",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,85319)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\debug-active-days\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\debug-active-days\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/debug-active-days/page",pathname:"/debug-active-days",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85319:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-active-days\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\debug-active-days\\page.tsx","default")},89881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687),a=r(43210),o=r(87979),i=r(91391),n=r(3582);function d(){let{isAdmin:e,loading:t}=(0,o.wC)(),[r,d]=(0,a.useState)([]),[c,l]=(0,a.useState)(!1),u=async()=>{try{l(!0),d([]),console.log("\uD83D\uDD0D Starting active days debug check...");let e=(await (0,i.CF)()).slice(0,10),t=[];for(let r of e)try{let e=r.activeDays||0,s=await (0,n.calculateUserActiveDays)(r.id),a=e!==s;t.push({userId:r.id,email:r.email,plan:r.plan,joinedDate:r.joinedDate,planExpiry:r.planExpiry,storedActiveDays:e,calculatedActiveDays:s,discrepancy:a,manuallySet:r.manuallySetActiveDays||!1,lastUpdate:r.lastActiveDaysUpdate}),console.log(`User ${r.email}: Stored=${e}, Calculated=${s}, Discrepancy=${a}`)}catch(e){console.error(`Error processing user ${r.id}:`,e),t.push({userId:r.id,email:r.email,error:e.message})}d(t),console.log("✅ Debug check completed")}catch(e){console.error("Error in debug check:",e)}finally{l(!1)}};return t?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):e?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,s.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Debug Active Days"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Compare stored Firestore values vs calculated values for active days"})]}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("button",{onClick:u,disabled:c,className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50",children:c?"Processing...":"Run Debug Check (First 10 Users)"}),r.length>0&&(0,s.jsxs)("div",{className:"mt-8",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Debug Results"}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plan"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stored Active Days"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Calculated Active Days"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Discrepancy"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Manually Set"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:r.map((e,t)=>(0,s.jsxs)("tr",{className:e.discrepancy?"bg-red-50":"",children:[(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.email}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.userId})]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.plan}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.storedActiveDays}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.calculatedActiveDays}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.discrepancy?(0,s.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800",children:"YES"}):(0,s.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:"NO"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.manuallySet?"Yes":"No"})]},t))})]})})]})]})]})})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Access Denied"}),(0,s.jsx)("p",{className:"text-gray-600",children:"You don't have permission to access this page."})]})})}},91391:(e,t,r)=>{"use strict";r.d(t,{CF:()=>l,I0:()=>p,TK:()=>m,getAdminDashboardStats:()=>n,getAllPendingWithdrawals:()=>x,getAllWithdrawals:()=>h,hG:()=>y,lo:()=>d,nQ:()=>u,r2:()=>g,updateWithdrawalStatus:()=>w,x5:()=>c});var s=r(75535),a=r(33784),o=r(3582);let i=new Map;async function n(){let e="dashboard-stats",t=function(e){let t=i.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let r=s.Dc.fromDate(t),n=await (0,s.getDocs)((0,s.collection)(a.db,o.COLLECTIONS.users)),d=n.size,c=(0,s.P)((0,s.collection)(a.db,o.COLLECTIONS.users),(0,s._M)(o.FIELD_NAMES.joinedDate,">=",r)),l=(await (0,s.getDocs)(c)).size,u=0,p=0,x=0,h=0;n.forEach(e=>{let r=e.data();u+=r[o.FIELD_NAMES.totalVideos]||0,p+=r[o.FIELD_NAMES.wallet]||0;let s=r[o.FIELD_NAMES.lastVideoDate]?.toDate();s&&s.toDateString()===t.toDateString()&&(x+=r[o.FIELD_NAMES.todayVideos]||0)});try{let e=(0,s.P)((0,s.collection)(a.db,o.COLLECTIONS.transactions),(0,s._M)(o.FIELD_NAMES.type,"==","video_earning"),(0,s.AB)(1e3));(await (0,s.getDocs)(e)).forEach(e=>{let r=e.data(),s=r[o.FIELD_NAMES.date]?.toDate();s&&s>=t&&(h+=r[o.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let g=(0,s.P)((0,s.collection)(a.db,o.COLLECTIONS.withdrawals),(0,s._M)("status","==","pending")),m=(await (0,s.getDocs)(g)).size,y=(0,s.P)((0,s.collection)(a.db,o.COLLECTIONS.withdrawals),(0,s._M)("date",">=",r)),w=(await (0,s.getDocs)(y)).size,E={totalUsers:d,totalVideos:u,totalEarnings:p,pendingWithdrawals:m,todayUsers:l,todayVideos:x,todayEarnings:h,todayWithdrawals:w};return i.set(e,{data:E,timestamp:Date.now()}),E}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function d(e=50,t=null){try{let r=(0,s.P)((0,s.collection)(a.db,o.COLLECTIONS.users),(0,s.My)(o.FIELD_NAMES.joinedDate,"desc"),(0,s.AB)(e));t&&(r=(0,s.P)((0,s.collection)(a.db,o.COLLECTIONS.users),(0,s.My)(o.FIELD_NAMES.joinedDate,"desc"),(0,s.HM)(t),(0,s.AB)(e)));let i=await (0,s.getDocs)(r);return{users:i.docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[o.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[o.FIELD_NAMES.planExpiry]?.toDate()})),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function c(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),r=(0,s.P)((0,s.collection)(a.db,o.COLLECTIONS.users),(0,s.My)(o.FIELD_NAMES.joinedDate,"desc"));return(await (0,s.getDocs)(r)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[o.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[o.FIELD_NAMES.planExpiry]?.toDate()})).filter(e=>{let r=String(e[o.FIELD_NAMES.name]||"").toLowerCase(),s=String(e[o.FIELD_NAMES.email]||"").toLowerCase(),a=String(e[o.FIELD_NAMES.mobile]||"").toLowerCase(),i=String(e[o.FIELD_NAMES.referralCode]||"").toLowerCase();return r.includes(t)||s.includes(t)||a.includes(t)||i.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function l(){try{let e=(0,s.P)((0,s.collection)(a.db,o.COLLECTIONS.users),(0,s.My)(o.FIELD_NAMES.joinedDate,"desc"));return(await (0,s.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[o.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[o.FIELD_NAMES.planExpiry]?.toDate()}))}catch(e){throw console.error("Error getting all users:",e),e}}async function u(){try{let e=(0,s.P)((0,s.collection)(a.db,o.COLLECTIONS.users));return(await (0,s.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function p(e=50,t=null){try{let r=(0,s.P)((0,s.collection)(a.db,o.COLLECTIONS.transactions),(0,s.My)(o.FIELD_NAMES.date,"desc"),(0,s.AB)(e));t&&(r=(0,s.P)((0,s.collection)(a.db,o.COLLECTIONS.transactions),(0,s.My)(o.FIELD_NAMES.date,"desc"),(0,s.HM)(t),(0,s.AB)(e)));let i=await (0,s.getDocs)(r);return{transactions:i.docs.map(e=>({id:e.id,...e.data(),date:e.data()[o.FIELD_NAMES.date]?.toDate()})),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function x(){try{console.log("\uD83D\uDD0D Loading ALL pending withdrawals...");let e=(0,s.P)((0,s.collection)(a.db,o.COLLECTIONS.withdrawals),(0,s._M)("status","==","pending"),(0,s.My)("date","desc")),t=(await (0,s.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()}));return console.log(`✅ Loaded ${t.length} pending withdrawals`),t}catch(e){throw console.error("Error getting all pending withdrawals:",e),e}}async function h(){try{console.log("\uD83D\uDD0D Loading ALL withdrawals...");let e=(0,s.P)((0,s.collection)(a.db,o.COLLECTIONS.withdrawals),(0,s.My)("date","desc")),t=(await (0,s.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()}));return console.log(`✅ Loaded ${t.length} total withdrawals`),t}catch(e){throw console.error("Error getting all withdrawals:",e),e}}async function g(){try{console.log("\uD83D\uDD0D Loading ALL transactions...");let e=(0,s.P)((0,s.collection)(a.db,o.COLLECTIONS.transactions),(0,s.My)(o.FIELD_NAMES.date,"desc")),t=(await (0,s.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data()[o.FIELD_NAMES.date]?.toDate()}));return console.log(`✅ Loaded ${t.length} total transactions`),t}catch(e){throw console.error("Error getting all transactions:",e),e}}async function m(e,t){try{await (0,s.mZ)((0,s.H9)(a.db,o.COLLECTIONS.users,e),t),i.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function y(e){try{await (0,s.kd)((0,s.H9)(a.db,o.COLLECTIONS.users,e)),i.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function w(e,t,n){try{let d=await (0,s.x7)((0,s.H9)(a.db,o.COLLECTIONS.withdrawals,e));if(!d.exists())throw Error("Withdrawal not found");let{userId:c,amount:l,status:u}=d.data(),p={status:t,updatedAt:s.Dc.now()};if(n&&(p.adminNotes=n),await (0,s.mZ)((0,s.H9)(a.db,o.COLLECTIONS.withdrawals,e),p),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(r.bind(r,3582));await e(c,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${l} processed for transfer`})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(r.bind(r,3582));await e(c,l),await t(c,{type:"withdrawal_rejected",amount:l,description:`Withdrawal rejected - ₹${l} credited back to wallet`})}i.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,6958,7567,8441,3582,7979],()=>r(7127));module.exports=s})();