(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698,6779,9160],{1469:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return l},getImageProps:function(){return o}});let s=a(8229),i=a(8883),r=a(3063),n=s._(a(1193));function o(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,a]of Object.entries(t))void 0===a&&delete t[e];return{props:t}}let l=r.Image},6273:(e,t,a)=>{"use strict";a.d(t,{x8:()=>p});var s=a(2144),i=a(6104);let r=(0,s.Qg)(i.Cn,"getUserDashboardData"),n=(0,s.Qg)(i.Cn,"submitVideoBatch"),o=(0,s.Qg)(i.Cn,"processWithdrawalRequest"),l=(0,s.Qg)(i.Cn,"getUserNotifications"),d=(0,s.Qg)(i.Cn,"getUserTransactions"),c=(0,s.Qg)(i.Cn,"getAdminWithdrawals"),h=(0,s.Qg)(i.Cn,"getAdminDashboardStats"),m=(0,s.Qg)(i.Cn,"getAdminUsers"),g=(0,s.Qg)(i.Cn,"getAdminNotifications"),x=(0,s.Qg)(i.Cn,"createAdminNotification");async function u(e){try{console.log("\uD83D\uDE80 Using optimized dashboard data function for user:",e),console.log("\uD83D\uDD17 Functions instance:",i.Cn.app.options.projectId);let t=await r({userId:e});if(console.log("\uD83D\uDCE1 Function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success){console.log("✅ Dashboard data loaded via optimized function");let t=e.data;return{userData:{name:t.user.name,email:t.user.email,mobile:t.user.mobile,referralCode:t.user.referralCode,plan:t.user.plan,planExpiry:null,activeDays:t.user.activeDays},walletData:{wallet:t.user.wallet},videoData:{totalVideos:t.videos.total,todayVideos:t.videos.today,remainingVideos:t.videos.remaining}}}throw console.error("❌ Function returned success: false",e),Error("Function returned success: false")}throw console.error("❌ Invalid function response structure:",t),Error("Invalid response from dashboard function")}catch(e){throw console.error("❌ Error in optimized dashboard data:",e),console.error("❌ Error details:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),e}}async function f(){try{console.log("\uD83D\uDE80 Using optimized admin dashboard stats function...");let e=await h({});if(e.data&&"object"==typeof e.data&&"success"in e.data){let t=e.data;if(t.success)return console.log("✅ Admin dashboard stats loaded via optimized function"),t.data}throw Error("Invalid response from admin dashboard stats function")}catch(e){throw console.error("❌ Error in optimized admin dashboard stats:",e),e}}let p={getDashboardData:async function(e){try{return await u(e)}catch(l){console.warn("⚠️ Optimized function failed, falling back to direct calls");let{getUserData:t,getWalletData:s,getVideoCountData:i}=await a.e(3592).then(a.bind(a,3592)),[r,n,o]=await Promise.all([t(e),s(e),i(e)]);return{userData:r,walletData:n,videoData:o}}},submitVideoBatch:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;try{console.log("\uD83D\uDE80 Using optimized video batch submission...");let a=await n({userId:e,videoCount:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Video batch submitted via optimized function"),e.data}throw Error("Invalid response from video batch function")}catch(e){throw console.error("❌ Error in optimized video batch submission:",e),e}},processWithdrawal:async function(e){try{console.log("\uD83D\uDE80 Using optimized withdrawal processing...");let t=await o(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Withdrawal processed via optimized function"),e.data}throw Error("Invalid response from withdrawal function")}catch(e){throw console.error("❌ Error in optimized withdrawal processing:",e),e}},getUserNotifications:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{console.log("\uD83D\uDE80 Using optimized notifications function...");let a=await l({userId:e,limit:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Notifications loaded via optimized function"),e.data}throw Error("Invalid response from notifications function")}catch(e){throw console.error("❌ Error in optimized notifications:",e),e}},getUserTransactions:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"all";try{console.log("\uD83D\uDE80 Using optimized transactions function...");let s=await d({userId:e,limit:t,type:a});if(s.data&&"object"==typeof s.data&&"success"in s.data){let e=s.data;if(e.success)return console.log("✅ Transactions loaded via optimized function"),e.data}throw Error("Invalid response from transactions function")}catch(e){throw console.error("❌ Error in optimized transactions:",e),e}},getAdminWithdrawals:async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{console.log("\uD83D\uDE80 Using optimized admin withdrawals function, showAll:",e),console.log("\uD83D\uDD17 Functions instance:",i.Cn.app.options.projectId);let t=await c({showAllWithdrawals:e});if(console.log("\uD83D\uDCE1 Admin withdrawals function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin withdrawals loaded via optimized function"),e.data;throw console.error("❌ Admin withdrawals function returned success: false",e),Error("Admin withdrawals function returned success: false")}throw console.error("❌ Invalid admin withdrawals function response structure:",t),Error("Invalid response from admin withdrawals function")}catch(e){throw console.error("❌ Error in optimized admin withdrawals:",e),console.error("❌ Error details:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),e}},getAdminDashboardStats:async function(){try{return await f()}catch(t){console.warn("⚠️ Optimized admin stats function failed, falling back to direct calls");let{getAdminDashboardStats:e}=await Promise.all([a.e(3592),a.e(6779)]).then(a.bind(a,6779));return await e()}},getAdminUsers:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{console.log("\uD83D\uDE80 Using optimized admin users function...");let t=await m(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin users loaded via optimized function"),e.data}throw Error("Invalid response from admin users function")}catch(e){throw console.error("❌ Error in optimized admin users:",e),e}},getAdminNotifications:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"all";try{console.log("\uD83D\uDE80 Using optimized admin notifications function...");let a=await g({limit:e,type:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Admin notifications loaded via optimized function"),e.data}throw Error("Invalid response from admin notifications function")}catch(e){throw console.error("❌ Error in optimized admin notifications:",e),e}},createAdminNotification:async function(e){try{console.log("\uD83D\uDE80 Using optimized admin notification creation...");let t=await x(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin notification created via optimized function"),e.data}throw Error("Invalid response from admin notification creation function")}catch(e){throw console.error("❌ Error in optimized admin notification creation:",e),e}},areFunctionsAvailable:async function(){try{console.log("\uD83D\uDD0D Testing Firebase Functions connectivity..."),console.log("\uD83D\uDD17 Functions project:",i.Cn.app.options.projectId),console.log("\uD83D\uDD17 Functions region:",i.Cn.region);let e=await r({userId:"test"});return console.log("✅ Functions are available, test response:",e),!0}catch(e){return console.warn("⚠️ Firebase Functions not available, falling back to direct Firestore"),console.error("❌ Functions test error:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),!1}}}},6766:(e,t,a)=>{"use strict";a.d(t,{default:()=>i.a});var s=a(1469),i=a.n(s)},6779:(e,t,a)=>{"use strict";a.d(t,{CF:()=>c,I0:()=>m,TK:()=>f,getAdminDashboardStats:()=>o,getAllPendingWithdrawals:()=>g,getAllWithdrawals:()=>x,hG:()=>p,lo:()=>l,nQ:()=>h,r2:()=>u,updateWithdrawalStatus:()=>w,x5:()=>d});var s=a(5317),i=a(6104),r=a(3592);let n=new Map;async function o(){let e="dashboard-stats",t=function(e){let t=n.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let a=s.Dc.fromDate(t),o=await (0,s.getDocs)((0,s.collection)(i.db,r.COLLECTIONS.users)),l=o.size,d=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.users),(0,s._M)(r.FIELD_NAMES.joinedDate,">=",a)),c=(await (0,s.getDocs)(d)).size,h=0,m=0,g=0,x=0;o.forEach(e=>{var a;let s=e.data();h+=s[r.FIELD_NAMES.totalVideos]||0,m+=s[r.FIELD_NAMES.wallet]||0;let i=null==(a=s[r.FIELD_NAMES.lastVideoDate])?void 0:a.toDate();i&&i.toDateString()===t.toDateString()&&(g+=s[r.FIELD_NAMES.todayVideos]||0)});try{let e=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.transactions),(0,s._M)(r.FIELD_NAMES.type,"==","video_earning"),(0,s.AB)(1e3));(await (0,s.getDocs)(e)).forEach(e=>{var a;let s=e.data(),i=null==(a=s[r.FIELD_NAMES.date])?void 0:a.toDate();i&&i>=t&&(x+=s[r.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let u=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.withdrawals),(0,s._M)("status","==","pending")),f=(await (0,s.getDocs)(u)).size,p=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.withdrawals),(0,s._M)("date",">=",a)),w=(await (0,s.getDocs)(p)).size,v={totalUsers:l,totalVideos:h,totalEarnings:m,pendingWithdrawals:f,todayUsers:c,todayVideos:g,todayEarnings:x,todayWithdrawals:w};return n.set(e,{data:v,timestamp:Date.now()}),v}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.users),(0,s.My)(r.FIELD_NAMES.joinedDate,"desc"),(0,s.AB)(e));t&&(a=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.users),(0,s.My)(r.FIELD_NAMES.joinedDate,"desc"),(0,s.HM)(t),(0,s.AB)(e)));let n=await (0,s.getDocs)(a);return{users:n.docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[r.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[r.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function d(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),a=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.users),(0,s.My)(r.FIELD_NAMES.joinedDate,"desc"));return(await (0,s.getDocs)(a)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[r.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[r.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}).filter(e=>{let a=String(e[r.FIELD_NAMES.name]||"").toLowerCase(),s=String(e[r.FIELD_NAMES.email]||"").toLowerCase(),i=String(e[r.FIELD_NAMES.mobile]||"").toLowerCase(),n=String(e[r.FIELD_NAMES.referralCode]||"").toLowerCase();return a.includes(t)||s.includes(t)||i.includes(t)||n.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function c(){try{let e=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.users),(0,s.My)(r.FIELD_NAMES.joinedDate,"desc"));return(await (0,s.getDocs)(e)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[r.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[r.FIELD_NAMES.planExpiry])?void 0:a.toDate()}})}catch(e){throw console.error("Error getting all users:",e),e}}async function h(){try{let e=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.users));return(await (0,s.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function m(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.transactions),(0,s.My)(r.FIELD_NAMES.date,"desc"),(0,s.AB)(e));t&&(a=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.transactions),(0,s.My)(r.FIELD_NAMES.date,"desc"),(0,s.HM)(t),(0,s.AB)(e)));let n=await (0,s.getDocs)(a);return{transactions:n.docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data()[r.FIELD_NAMES.date])?void 0:t.toDate()}}),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function g(){try{console.log("\uD83D\uDD0D Loading ALL pending withdrawals...");let e=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.withdrawals),(0,s._M)("status","==","pending"),(0,s.My)("date","desc")),t=(await (0,s.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," pending withdrawals")),t}catch(e){throw console.error("Error getting all pending withdrawals:",e),e}}async function x(){try{console.log("\uD83D\uDD0D Loading ALL withdrawals...");let e=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.withdrawals),(0,s.My)("date","desc")),t=(await (0,s.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," total withdrawals")),t}catch(e){throw console.error("Error getting all withdrawals:",e),e}}async function u(){try{console.log("\uD83D\uDD0D Loading ALL transactions...");let e=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.transactions),(0,s.My)(r.FIELD_NAMES.date,"desc")),t=(await (0,s.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data()[r.FIELD_NAMES.date])?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," total transactions")),t}catch(e){throw console.error("Error getting all transactions:",e),e}}async function f(e,t){try{await (0,s.mZ)((0,s.H9)(i.db,r.COLLECTIONS.users,e),t),n.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function p(e){try{await (0,s.kd)((0,s.H9)(i.db,r.COLLECTIONS.users,e)),n.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function w(e,t,o){try{let l=await (0,s.x7)((0,s.H9)(i.db,r.COLLECTIONS.withdrawals,e));if(!l.exists())throw Error("Withdrawal not found");let{userId:d,amount:c,status:h}=l.data(),m={status:t,updatedAt:s.Dc.now()};if(o&&(m.adminNotes=o),await (0,s.mZ)((0,s.H9)(i.db,r.COLLECTIONS.withdrawals,e),m),"approved"===t&&"approved"!==h){let{addTransaction:e}=await Promise.resolve().then(a.bind(a,3592));await e(d,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(c," processed for transfer")})}if("rejected"===t&&"rejected"!==h){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(a.bind(a,3592));await e(d,c),await t(d,{type:"withdrawal_rejected",amount:c,description:"Withdrawal rejected - ₹".concat(c," credited back to wallet")})}n.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},7220:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var s=a(5155),i=a(2115),r=a(6874),n=a.n(r),o=a(6766),l=a(6681),d=a(6779),c=a(6273),h=a(12);function m(){var e,t,a,r,m;let{user:g,loading:x,isAdmin:u}=(0,l.wC)(),[f,p]=(0,i.useState)(null),[w,v]=(0,i.useState)(!0),[y,N]=(0,i.useState)(!1);(0,i.useEffect)(()=>{u&&j()},[u]);let j=async()=>{try{v(!0);try{console.log("\uD83D\uDE80 Loading admin dashboard with optimized function...");let e=await c.x8.getAdminDashboardStats();p(e),console.log("✅ Admin dashboard loaded via optimized function")}catch(t){console.warn("⚠️ Optimized function failed, using fallback:",t);let e=await (0,d.getAdminDashboardStats)();p(e)}}catch(e){console.error("Error loading dashboard stats:",e)}finally{v(!1)}};return x||w?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"spinner"})}):(0,s.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,s.jsxs)("aside",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 transform ".concat(y?"translate-x-0":"-translate-x-full"," transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0"),children:[(0,s.jsxs)("div",{className:"flex items-center justify-center h-16 bg-gray-900",children:[(0,s.jsx)(o.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:32,height:32,className:"mr-2"}),(0,s.jsx)("span",{className:"text-white text-xl font-bold",children:"MyTube Admin"})]}),(0,s.jsx)("nav",{className:"mt-8",children:(0,s.jsxs)("div",{className:"px-4 space-y-2",children:[(0,s.jsxs)(n(),{href:"/admin",className:"flex items-center px-4 py-2 text-white bg-gray-700 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-tachometer-alt mr-3"}),"Dashboard"]}),(0,s.jsxs)(n(),{href:"/admin/users",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-users mr-3"}),"Users"]}),(0,s.jsxs)(n(),{href:"/admin/simple-upload",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-file-csv mr-3"}),"Simple Upload"]}),(0,s.jsxs)(n(),{href:"/admin/transactions",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-exchange-alt mr-3"}),"Transactions"]}),(0,s.jsxs)(n(),{href:"/admin/withdrawals",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-money-bill-wave mr-3"}),"Withdrawals"]}),(0,s.jsxs)(n(),{href:"/admin/notifications",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-bell mr-3"}),"Notifications"]}),(0,s.jsxs)(n(),{href:"/admin/leaves",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-calendar-times mr-3"}),"Leave Management"]}),(0,s.jsxs)(n(),{href:"/admin/settings",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-cog mr-3"}),"Settings"]}),(0,s.jsxs)(n(),{href:"/admin/fix-active-days",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-tools mr-3"}),"Fix Active Days"]}),(0,s.jsxs)(n(),{href:"/admin/fix-plan-expiry",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-calendar-times mr-3"}),"Fix Plan Expiry"]}),(0,s.jsxs)(n(),{href:"/admin/daily-active-days",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-calendar-plus mr-3"}),"Daily Active Days"]})]})}),(0,s.jsx)("div",{className:"absolute bottom-4 left-4 right-4",children:(0,s.jsxs)("button",{onClick:()=>{(0,h._f)(null==g?void 0:g.uid,"/admin/login")},className:"w-full flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-sign-out-alt mr-3"}),"Logout"]})})]}),(0,s.jsxs)("div",{className:"lg:ml-64",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,s.jsx)("button",{onClick:()=>N(!y),className:"lg:hidden text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-bars text-xl"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("span",{className:"text-gray-700",children:"Welcome, Admin"}),(0,s.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,s.jsx)("i",{className:"fas fa-user-shield text-gray-600"})})]})]})}),(0,s.jsxs)("main",{className:"p-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,s.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,s.jsx)("i",{className:"fas fa-users text-blue-600 text-xl"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Users"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==f||null==(e=f.totalUsers)?void 0:e.toLocaleString())||"0"})]})]})}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,s.jsx)("i",{className:"fas fa-video text-green-600 text-xl"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Videos"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==f||null==(t=f.totalVideos)?void 0:t.toLocaleString())||"0"})]})]})}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,s.jsx)("i",{className:"fas fa-rupee-sign text-yellow-600 text-xl"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Earnings"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₹",(null==f||null==(a=f.totalEarnings)?void 0:a.toLocaleString())||"0"]})]})]})}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,s.jsx)("i",{className:"fas fa-clock text-red-600 text-xl"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending Withdrawals"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==f?void 0:f.pendingWithdrawals)||"0"})]})]})})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow mb-8",children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Today's Activity"})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-3xl font-bold text-blue-600",children:(null==f?void 0:f.todayUsers)||"0"}),(0,s.jsx)("p",{className:"text-gray-600",children:"New Users"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-3xl font-bold text-green-600",children:(null==f||null==(r=f.todayVideos)?void 0:r.toLocaleString())||"0"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Videos Watched"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("p",{className:"text-3xl font-bold text-yellow-600",children:["₹",(null==f||null==(m=f.todayEarnings)?void 0:m.toLocaleString())||"0"]}),(0,s.jsx)("p",{className:"text-gray-600",children:"Earnings Paid"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-3xl font-bold text-red-600",children:(null==f?void 0:f.todayWithdrawals)||"0"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Withdrawals"})]})]})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsx)(n(),{href:"/admin/users",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,s.jsx)("i",{className:"fas fa-users text-blue-600 text-2xl"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Manage Users"}),(0,s.jsx)("p",{className:"text-gray-600",children:"View and manage user accounts"})]})]})}),(0,s.jsx)(n(),{href:"/admin/withdrawals",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,s.jsx)("i",{className:"fas fa-money-bill-wave text-green-600 text-2xl"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Process Withdrawals"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Review and approve withdrawals"})]})]})}),(0,s.jsx)(n(),{href:"/admin/notifications",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-3 bg-yellow-100 rounded-lg",children:(0,s.jsx)("i",{className:"fas fa-bell text-yellow-600 text-2xl"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Send Notifications"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Notify users about updates"})]})]})}),(0,s.jsx)(n(),{href:"/admin/settings",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-3 bg-purple-100 rounded-lg",children:(0,s.jsx)("i",{className:"fas fa-cog text-purple-600 text-2xl"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"System Settings"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Configure platform settings"})]})]})}),(0,s.jsx)(n(),{href:"/admin/fix-active-days",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-3 bg-red-100 rounded-lg",children:(0,s.jsx)("i",{className:"fas fa-tools text-red-600 text-2xl"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Fix Active Days"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Fix daily counts and active days"})]})]})}),(0,s.jsx)(n(),{href:"/admin/fix-plan-expiry",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-3 bg-orange-100 rounded-lg",children:(0,s.jsx)("i",{className:"fas fa-calendar-times text-orange-600 text-2xl"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Fix Plan Expiry"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Fix missing plan expiry dates"})]})]})}),(0,s.jsx)(n(),{href:"/admin/simple-upload",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,s.jsx)("i",{className:"fas fa-file-csv text-green-600 text-2xl"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Simple Upload"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Update videos, wallet & active days via CSV"})]})]})}),(0,s.jsx)(n(),{href:"/admin/daily-active-days",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-3 bg-indigo-100 rounded-lg",children:(0,s.jsx)("i",{className:"fas fa-calendar-plus text-indigo-600 text-2xl"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Daily Active Days"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Manage daily active days increment"})]})]})})]})]})]}),y&&(0,s.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>N(!1)})]})}},8856:(e,t,a)=>{Promise.resolve().then(a.bind(a,7220))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6874,3063,3592,6681,8441,1684,7358],()=>t(8856)),_N_E=e.O()}]);