import { db } from '@/lib/firebase'
import { collection, getDocs, doc, updateDoc, writeBatch } from 'firebase/firestore'
import { COLLECTIONS, FIELD_NAMES } from './dataService'

interface UserRestoreData {
  userId: string
  email: string
  plan: string
  currentActiveDays: number
  joinedDate: Date
  daysSinceJoined: number
  suggestedActiveDays: number
  shouldRestore: boolean
  reason: string
}

export async function analyzeAndRestoreActiveDays(): Promise<{
  analyzed: number
  needsRestoration: UserRestoreData[]
  restored: number
  errors: string[]
}> {
  const results = {
    analyzed: 0,
    needsRestoration: [] as UserRestoreData[],
    restored: 0,
    errors: [] as string[]
  }

  try {
    console.log('🔍 Starting active days restoration analysis...')
    
    // Get all users
    const usersSnapshot = await getDocs(collection(db, COLLECTIONS.users))
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    for (const userDoc of usersSnapshot.docs) {
      try {
        const userData = userDoc.data()
        const userId = userDoc.id
        results.analyzed++

        // Skip if no joined date
        if (!userData[FIELD_NAMES.joinedDate]) {
          continue
        }

        const joinedDate = userData[FIELD_NAMES.joinedDate].toDate()
        joinedDate.setHours(0, 0, 0, 0)
        
        const currentActiveDays = userData[FIELD_NAMES.activeDays] || 1
        const plan = userData[FIELD_NAMES.plan] || 'Trial'
        const manuallySet = userData[FIELD_NAMES.manuallySetActiveDays] || false
        
        // Calculate days since joined
        const daysSinceJoined = Math.floor((today.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24))
        
        let suggestedActiveDays = 1
        let shouldRestore = false
        let reason = ''

        // Skip manually set users
        if (manuallySet) {
          reason = 'Manually set by admin - skipping'
        } else {
          // Calculate what active days should be
          if (plan === 'Trial') {
            // For trial users: max should be days since joined + 1, but realistically should be much lower
            const maxReasonableActiveDays = Math.min(daysSinceJoined + 1, 10) // Cap at 10 for trials
            
            if (currentActiveDays > maxReasonableActiveDays) {
              suggestedActiveDays = Math.max(1, Math.min(daysSinceJoined + 1, 3)) // Trial should be 1-3 days
              shouldRestore = true
              reason = `Trial user with ${currentActiveDays} active days (joined ${daysSinceJoined} days ago)`
            }
          } else {
            // For paid users: should not exceed days since joined by much
            const maxReasonableActiveDays = daysSinceJoined + 5 // Allow some buffer
            
            if (currentActiveDays > maxReasonableActiveDays) {
              // For paid users, suggest a reasonable number based on join date
              suggestedActiveDays = Math.max(1, Math.min(daysSinceJoined + 1, 30)) // Cap at 30 for paid
              shouldRestore = true
              reason = `Paid user with ${currentActiveDays} active days (joined ${daysSinceJoined} days ago)`
            }
          }
        }

        if (shouldRestore) {
          results.needsRestoration.push({
            userId,
            email: userData[FIELD_NAMES.email] || 'Unknown',
            plan,
            currentActiveDays,
            joinedDate,
            daysSinceJoined,
            suggestedActiveDays,
            shouldRestore,
            reason
          })
        }

      } catch (error) {
        results.errors.push(`Error processing user ${userDoc.id}: ${error}`)
      }
    }

    console.log(`✅ Analysis complete. Found ${results.needsRestoration.length} users needing restoration out of ${results.analyzed} total users.`)
    
    return results

  } catch (error) {
    console.error('Error in active days restoration analysis:', error)
    results.errors.push(`Analysis error: ${error}`)
    return results
  }
}

export async function restoreActiveDaysForUsers(usersToRestore: UserRestoreData[]): Promise<{
  restored: number
  errors: string[]
}> {
  const results = {
    restored: 0,
    errors: [] as string[]
  }

  try {
    console.log(`🔧 Starting restoration for ${usersToRestore.length} users...`)
    
    // Use batch writes for efficiency
    const batchSize = 500
    let currentBatch = writeBatch(db)
    let operationsInBatch = 0

    for (const user of usersToRestore) {
      try {
        const userRef = doc(db, COLLECTIONS.users, user.userId)
        
        currentBatch.update(userRef, {
          [FIELD_NAMES.activeDays]: user.suggestedActiveDays,
          [FIELD_NAMES.manuallySetActiveDays]: false, // Reset manual flag
          [FIELD_NAMES.lastActiveDaysUpdate]: new Date()
        })
        
        operationsInBatch++
        results.restored++

        // Commit batch if it's full
        if (operationsInBatch >= batchSize) {
          await currentBatch.commit()
          currentBatch = writeBatch(db)
          operationsInBatch = 0
          console.log(`📦 Committed batch of ${batchSize} updates`)
        }

      } catch (error) {
        results.errors.push(`Error restoring user ${user.email}: ${error}`)
      }
    }

    // Commit remaining operations
    if (operationsInBatch > 0) {
      await currentBatch.commit()
      console.log(`📦 Committed final batch of ${operationsInBatch} updates`)
    }

    console.log(`✅ Restoration complete. Restored ${results.restored} users.`)
    
    return results

  } catch (error) {
    console.error('Error in active days restoration:', error)
    results.errors.push(`Restoration error: ${error}`)
    return results
  }
}

export async function createActiveDaysBackup(): Promise<{
  success: boolean
  backupData: any[]
  error?: string
}> {
  try {
    console.log('💾 Creating active days backup...')
    
    const usersSnapshot = await getDocs(collection(db, COLLECTIONS.users))
    const backupData = []

    for (const userDoc of usersSnapshot.docs) {
      const userData = userDoc.data()
      backupData.push({
        userId: userDoc.id,
        email: userData[FIELD_NAMES.email],
        activeDays: userData[FIELD_NAMES.activeDays],
        manuallySetActiveDays: userData[FIELD_NAMES.manuallySetActiveDays],
        lastActiveDaysUpdate: userData[FIELD_NAMES.lastActiveDaysUpdate],
        plan: userData[FIELD_NAMES.plan],
        joinedDate: userData[FIELD_NAMES.joinedDate],
        backupTimestamp: new Date()
      })
    }

    console.log(`✅ Backup created for ${backupData.length} users`)
    
    return {
      success: true,
      backupData
    }

  } catch (error) {
    console.error('Error creating backup:', error)
    return {
      success: false,
      backupData: [],
      error: String(error)
    }
  }
}
