"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6779,9160],{6779:(t,a,e)=>{e.d(a,{CF:()=>c,I0:()=>u,TK:()=>D,getAdminDashboardStats:()=>s,getAllPendingWithdrawals:()=>w,getAllWithdrawals:()=>L,hG:()=>g,lo:()=>l,nQ:()=>E,r2:()=>h,updateWithdrawalStatus:()=>S,x5:()=>i});var o=e(5317),r=e(6104),n=e(3592);let d=new Map;async function s(){let t="dashboard-stats",a=function(t){let a=d.get(t);return a&&Date.now()-a.timestamp<3e5?a.data:null}(t);if(a)return a;try{let a=new Date;a.setHours(0,0,0,0);let e=o.Dc.fromDate(a),s=await (0,o.getDocs)((0,o.collection)(r.db,n.COLLECTIONS.users)),l=s.size,i=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.users),(0,o._M)(n.FIELD_NAMES.joinedDate,">=",e)),c=(await (0,o.getDocs)(i)).size,E=0,u=0,w=0,L=0;s.forEach(t=>{var e;let o=t.data();E+=o[n.FIELD_NAMES.totalVideos]||0,u+=o[n.FIELD_NAMES.wallet]||0;let r=null==(e=o[n.FIELD_NAMES.lastVideoDate])?void 0:e.toDate();r&&r.toDateString()===a.toDateString()&&(w+=o[n.FIELD_NAMES.todayVideos]||0)});try{let t=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.transactions),(0,o._M)(n.FIELD_NAMES.type,"==","video_earning"),(0,o.AB)(1e3));(await (0,o.getDocs)(t)).forEach(t=>{var e;let o=t.data(),r=null==(e=o[n.FIELD_NAMES.date])?void 0:e.toDate();r&&r>=a&&(L+=o[n.FIELD_NAMES.amount]||0)})}catch(t){console.warn("Could not fetch today's transactions:",t)}let h=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.withdrawals),(0,o._M)("status","==","pending")),D=(await (0,o.getDocs)(h)).size,g=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.withdrawals),(0,o._M)("date",">=",e)),S=(await (0,o.getDocs)(g)).size,N={totalUsers:l,totalVideos:E,totalEarnings:u,pendingWithdrawals:D,todayUsers:c,todayVideos:w,todayEarnings:L,todayWithdrawals:S};return d.set(t,{data:N,timestamp:Date.now()}),N}catch(t){throw console.error("Error getting admin dashboard stats:",t),t}}async function l(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let e=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.users),(0,o.My)(n.FIELD_NAMES.joinedDate,"desc"),(0,o.AB)(t));a&&(e=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.users),(0,o.My)(n.FIELD_NAMES.joinedDate,"desc"),(0,o.HM)(a),(0,o.AB)(t)));let d=await (0,o.getDocs)(e);return{users:d.docs.map(t=>{var a,e;return{id:t.id,...t.data(),joinedDate:null==(a=t.data()[n.FIELD_NAMES.joinedDate])?void 0:a.toDate(),planExpiry:null==(e=t.data()[n.FIELD_NAMES.planExpiry])?void 0:e.toDate()}}),lastDoc:d.docs[d.docs.length-1]||null,hasMore:d.docs.length===t}}catch(t){throw console.error("Error getting users:",t),t}}async function i(t){try{if(!t||0===t.trim().length)return[];let a=t.toLowerCase().trim(),e=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.users),(0,o.My)(n.FIELD_NAMES.joinedDate,"desc"));return(await (0,o.getDocs)(e)).docs.map(t=>{var a,e;return{id:t.id,...t.data(),joinedDate:null==(a=t.data()[n.FIELD_NAMES.joinedDate])?void 0:a.toDate(),planExpiry:null==(e=t.data()[n.FIELD_NAMES.planExpiry])?void 0:e.toDate()}}).filter(t=>{let e=String(t[n.FIELD_NAMES.name]||"").toLowerCase(),o=String(t[n.FIELD_NAMES.email]||"").toLowerCase(),r=String(t[n.FIELD_NAMES.mobile]||"").toLowerCase(),d=String(t[n.FIELD_NAMES.referralCode]||"").toLowerCase();return e.includes(a)||o.includes(a)||r.includes(a)||d.includes(a)})}catch(t){throw console.error("Error searching users:",t),t}}async function c(){try{let t=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.users),(0,o.My)(n.FIELD_NAMES.joinedDate,"desc"));return(await (0,o.getDocs)(t)).docs.map(t=>{var a,e;return{id:t.id,...t.data(),joinedDate:null==(a=t.data()[n.FIELD_NAMES.joinedDate])?void 0:a.toDate(),planExpiry:null==(e=t.data()[n.FIELD_NAMES.planExpiry])?void 0:e.toDate()}})}catch(t){throw console.error("Error getting all users:",t),t}}async function E(){try{let t=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.users));return(await (0,o.getDocs)(t)).size}catch(t){throw console.error("Error getting total user count:",t),t}}async function u(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let e=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.transactions),(0,o.My)(n.FIELD_NAMES.date,"desc"),(0,o.AB)(t));a&&(e=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.transactions),(0,o.My)(n.FIELD_NAMES.date,"desc"),(0,o.HM)(a),(0,o.AB)(t)));let d=await (0,o.getDocs)(e);return{transactions:d.docs.map(t=>{var a;return{id:t.id,...t.data(),date:null==(a=t.data()[n.FIELD_NAMES.date])?void 0:a.toDate()}}),lastDoc:d.docs[d.docs.length-1]||null,hasMore:d.docs.length===t}}catch(t){throw console.error("Error getting transactions:",t),t}}async function w(){try{console.log("\uD83D\uDD0D Loading ALL pending withdrawals...");let t=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.withdrawals),(0,o._M)("status","==","pending"),(0,o.My)("date","desc")),a=(await (0,o.getDocs)(t)).docs.map(t=>{var a;return{id:t.id,...t.data(),date:null==(a=t.data().date)?void 0:a.toDate()}});return console.log("✅ Loaded ".concat(a.length," pending withdrawals")),a}catch(t){throw console.error("Error getting all pending withdrawals:",t),t}}async function L(){try{console.log("\uD83D\uDD0D Loading ALL withdrawals...");let t=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.withdrawals),(0,o.My)("date","desc")),a=(await (0,o.getDocs)(t)).docs.map(t=>{var a;return{id:t.id,...t.data(),date:null==(a=t.data().date)?void 0:a.toDate()}});return console.log("✅ Loaded ".concat(a.length," total withdrawals")),a}catch(t){throw console.error("Error getting all withdrawals:",t),t}}async function h(){try{console.log("\uD83D\uDD0D Loading ALL transactions...");let t=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.transactions),(0,o.My)(n.FIELD_NAMES.date,"desc")),a=(await (0,o.getDocs)(t)).docs.map(t=>{var a;return{id:t.id,...t.data(),date:null==(a=t.data()[n.FIELD_NAMES.date])?void 0:a.toDate()}});return console.log("✅ Loaded ".concat(a.length," total transactions")),a}catch(t){throw console.error("Error getting all transactions:",t),t}}async function D(t,a){try{await (0,o.mZ)((0,o.H9)(r.db,n.COLLECTIONS.users,t),a),d.delete("dashboard-stats")}catch(t){throw console.error("Error updating user:",t),t}}async function g(t){try{await (0,o.kd)((0,o.H9)(r.db,n.COLLECTIONS.users,t)),d.delete("dashboard-stats")}catch(t){throw console.error("Error deleting user:",t),t}}async function S(t,a,s){try{let l=await (0,o.x7)((0,o.H9)(r.db,n.COLLECTIONS.withdrawals,t));if(!l.exists())throw Error("Withdrawal not found");let{userId:i,amount:c,status:E}=l.data(),u={status:a,updatedAt:o.Dc.now()};if(s&&(u.adminNotes=s),await (0,o.mZ)((0,o.H9)(r.db,n.COLLECTIONS.withdrawals,t),u),"approved"===a&&"approved"!==E){let{addTransaction:t}=await Promise.resolve().then(e.bind(e,3592));await t(i,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(c," processed for transfer")})}if("rejected"===a&&"rejected"!==E){let{updateWalletBalance:t,addTransaction:a}=await Promise.resolve().then(e.bind(e,3592));await t(i,c),await a(i,{type:"withdrawal_rejected",amount:c,description:"Withdrawal rejected - ₹".concat(c," credited back to wallet")})}d.delete("dashboard-stats")}catch(t){throw console.error("Error updating withdrawal status:",t),t}}}}]);