(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6779,8925,9160],{3:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var s=a(5155),r=a(2115),l=a(6681),n=a(6779),o=a(3592);function i(){let{isAdmin:e,loading:t}=(0,l.wC)(),[a,i]=(0,r.useState)([]),[c,d]=(0,r.useState)(!1),h=async()=>{try{d(!0),i([]),console.log("\uD83D\uDD0D Starting active days debug check...");let e=(await (0,n.CF)()).slice(0,10),t=[];for(let a of e)try{let e=a.activeDays||0,s=await (0,o.calculateUserActiveDays)(a.id),r=e!==s;t.push({userId:a.id,email:a.email,plan:a.plan,joinedDate:a.joinedDate,planExpiry:a.planExpiry,storedActiveDays:e,calculatedActiveDays:s,discrepancy:r,manuallySet:a.manuallySetActiveDays||!1,lastUpdate:a.lastActiveDaysUpdate}),console.log("User ".concat(a.email,": Stored=").concat(e,", Calculated=").concat(s,", Discrepancy=").concat(r))}catch(e){console.error("Error processing user ".concat(a.id,":"),e),t.push({userId:a.id,email:a.email,error:e.message})}i(t),console.log("✅ Debug check completed")}catch(e){console.error("Error in debug check:",e)}finally{d(!1)}};return t?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):e?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,s.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Debug Active Days"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Compare stored Firestore values vs calculated values for active days"})]}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("button",{onClick:h,disabled:c,className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50",children:c?"Processing...":"Run Debug Check (First 10 Users)"}),a.length>0&&(0,s.jsxs)("div",{className:"mt-8",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Debug Results"}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plan"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stored Active Days"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Calculated Active Days"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Discrepancy"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Manually Set"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:a.map((e,t)=>(0,s.jsxs)("tr",{className:e.discrepancy?"bg-red-50":"",children:[(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.email}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.userId})]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.plan}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.storedActiveDays}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.calculatedActiveDays}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.discrepancy?(0,s.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800",children:"YES"}):(0,s.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:"NO"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.manuallySet?"Yes":"No"})]},t))})]})})]})]})]})})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Access Denied"}),(0,s.jsx)("p",{className:"text-gray-600",children:"You don't have permission to access this page."})]})})}},6463:(e,t,a)=>{Promise.resolve().then(a.bind(a,3))},6779:(e,t,a)=>{"use strict";a.d(t,{CF:()=>d,I0:()=>u,TK:()=>m,getAdminDashboardStats:()=>o,getAllPendingWithdrawals:()=>p,getAllWithdrawals:()=>x,hG:()=>w,lo:()=>i,nQ:()=>h,r2:()=>g,updateWithdrawalStatus:()=>y,x5:()=>c});var s=a(5317),r=a(6104),l=a(3592);let n=new Map;async function o(){let e="dashboard-stats",t=function(e){let t=n.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let a=s.Dc.fromDate(t),o=await (0,s.getDocs)((0,s.collection)(r.db,l.COLLECTIONS.users)),i=o.size,c=(0,s.P)((0,s.collection)(r.db,l.COLLECTIONS.users),(0,s._M)(l.FIELD_NAMES.joinedDate,">=",a)),d=(await (0,s.getDocs)(c)).size,h=0,u=0,p=0,x=0;o.forEach(e=>{var a;let s=e.data();h+=s[l.FIELD_NAMES.totalVideos]||0,u+=s[l.FIELD_NAMES.wallet]||0;let r=null==(a=s[l.FIELD_NAMES.lastVideoDate])?void 0:a.toDate();r&&r.toDateString()===t.toDateString()&&(p+=s[l.FIELD_NAMES.todayVideos]||0)});try{let e=(0,s.P)((0,s.collection)(r.db,l.COLLECTIONS.transactions),(0,s._M)(l.FIELD_NAMES.type,"==","video_earning"),(0,s.AB)(1e3));(await (0,s.getDocs)(e)).forEach(e=>{var a;let s=e.data(),r=null==(a=s[l.FIELD_NAMES.date])?void 0:a.toDate();r&&r>=t&&(x+=s[l.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let g=(0,s.P)((0,s.collection)(r.db,l.COLLECTIONS.withdrawals),(0,s._M)("status","==","pending")),m=(await (0,s.getDocs)(g)).size,w=(0,s.P)((0,s.collection)(r.db,l.COLLECTIONS.withdrawals),(0,s._M)("date",">=",a)),y=(await (0,s.getDocs)(w)).size,E={totalUsers:i,totalVideos:h,totalEarnings:u,pendingWithdrawals:m,todayUsers:d,todayVideos:p,todayEarnings:x,todayWithdrawals:y};return n.set(e,{data:E,timestamp:Date.now()}),E}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,s.P)((0,s.collection)(r.db,l.COLLECTIONS.users),(0,s.My)(l.FIELD_NAMES.joinedDate,"desc"),(0,s.AB)(e));t&&(a=(0,s.P)((0,s.collection)(r.db,l.COLLECTIONS.users),(0,s.My)(l.FIELD_NAMES.joinedDate,"desc"),(0,s.HM)(t),(0,s.AB)(e)));let n=await (0,s.getDocs)(a);return{users:n.docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[l.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[l.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function c(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),a=(0,s.P)((0,s.collection)(r.db,l.COLLECTIONS.users),(0,s.My)(l.FIELD_NAMES.joinedDate,"desc"));return(await (0,s.getDocs)(a)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[l.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[l.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}).filter(e=>{let a=String(e[l.FIELD_NAMES.name]||"").toLowerCase(),s=String(e[l.FIELD_NAMES.email]||"").toLowerCase(),r=String(e[l.FIELD_NAMES.mobile]||"").toLowerCase(),n=String(e[l.FIELD_NAMES.referralCode]||"").toLowerCase();return a.includes(t)||s.includes(t)||r.includes(t)||n.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function d(){try{let e=(0,s.P)((0,s.collection)(r.db,l.COLLECTIONS.users),(0,s.My)(l.FIELD_NAMES.joinedDate,"desc"));return(await (0,s.getDocs)(e)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[l.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[l.FIELD_NAMES.planExpiry])?void 0:a.toDate()}})}catch(e){throw console.error("Error getting all users:",e),e}}async function h(){try{let e=(0,s.P)((0,s.collection)(r.db,l.COLLECTIONS.users));return(await (0,s.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function u(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,s.P)((0,s.collection)(r.db,l.COLLECTIONS.transactions),(0,s.My)(l.FIELD_NAMES.date,"desc"),(0,s.AB)(e));t&&(a=(0,s.P)((0,s.collection)(r.db,l.COLLECTIONS.transactions),(0,s.My)(l.FIELD_NAMES.date,"desc"),(0,s.HM)(t),(0,s.AB)(e)));let n=await (0,s.getDocs)(a);return{transactions:n.docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data()[l.FIELD_NAMES.date])?void 0:t.toDate()}}),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function p(){try{console.log("\uD83D\uDD0D Loading ALL pending withdrawals...");let e=(0,s.P)((0,s.collection)(r.db,l.COLLECTIONS.withdrawals),(0,s._M)("status","==","pending"),(0,s.My)("date","desc")),t=(await (0,s.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," pending withdrawals")),t}catch(e){throw console.error("Error getting all pending withdrawals:",e),e}}async function x(){try{console.log("\uD83D\uDD0D Loading ALL withdrawals...");let e=(0,s.P)((0,s.collection)(r.db,l.COLLECTIONS.withdrawals),(0,s.My)("date","desc")),t=(await (0,s.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," total withdrawals")),t}catch(e){throw console.error("Error getting all withdrawals:",e),e}}async function g(){try{console.log("\uD83D\uDD0D Loading ALL transactions...");let e=(0,s.P)((0,s.collection)(r.db,l.COLLECTIONS.transactions),(0,s.My)(l.FIELD_NAMES.date,"desc")),t=(await (0,s.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data()[l.FIELD_NAMES.date])?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," total transactions")),t}catch(e){throw console.error("Error getting all transactions:",e),e}}async function m(e,t){try{await (0,s.mZ)((0,s.H9)(r.db,l.COLLECTIONS.users,e),t),n.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function w(e){try{await (0,s.kd)((0,s.H9)(r.db,l.COLLECTIONS.users,e)),n.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function y(e,t,o){try{let i=await (0,s.x7)((0,s.H9)(r.db,l.COLLECTIONS.withdrawals,e));if(!i.exists())throw Error("Withdrawal not found");let{userId:c,amount:d,status:h}=i.data(),u={status:t,updatedAt:s.Dc.now()};if(o&&(u.adminNotes=o),await (0,s.mZ)((0,s.H9)(r.db,l.COLLECTIONS.withdrawals,e),u),"approved"===t&&"approved"!==h){let{addTransaction:e}=await Promise.resolve().then(a.bind(a,3592));await e(c,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(d," processed for transfer")})}if("rejected"===t&&"rejected"!==h){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(a.bind(a,3592));await e(c,d),await t(c,{type:"withdrawal_rejected",amount:d,description:"Withdrawal rejected - ₹".concat(d," credited back to wallet")})}n.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,3592,6681,8441,1684,7358],()=>t(6463)),_N_E=e.O()}]);