'use client'

import { useState } from 'react'
import { useRequireAdmin } from '@/hooks/useAuth'
import { getAllUsers } from '@/lib/adminDataService'
import { calculateUserActiveDays } from '@/lib/dataService'
import { optimizedService } from '@/lib/optimizedDataService'
import { analyzeAndRestoreActiveDays, restoreActiveDaysForUsers, createActiveDaysBackup } from '@/lib/activeDaysRestoration'
import Swal from 'sweetalert2'

export default function DebugActiveDaysPage() {
  const { isAdmin, loading } = useRequireAdmin()
  const [debugResults, setDebugResults] = useState<any[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [isTestingFirebaseFunction, setIsTestingFirebaseFunction] = useState(false)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysisResults, setAnalysisResults] = useState<any>(null)
  const [isRestoring, setIsRestoring] = useState(false)
  const [restorationResults, setRestorationResults] = useState<any>(null)

  const runDebugCheck = async () => {
    try {
      setIsProcessing(true)
      setDebugResults([])

      console.log('🔍 Starting active days debug check...')

      // Get first 10 users for debugging
      const allUsers = await getAllUsers()
      const sampleUsers = allUsers.slice(0, 10)

      const results = []

      for (const user of sampleUsers) {
        try {
          // Get stored value from Firestore
          const storedActiveDays = (user as any).activeDays || 0

          // Get calculated value from centralized function
          const calculatedActiveDays = await calculateUserActiveDays(user.id)

          // Check if there's a discrepancy
          const discrepancy = storedActiveDays !== calculatedActiveDays

          results.push({
            userId: user.id,
            email: (user as any).email,
            plan: (user as any).plan,
            joinedDate: (user as any).joinedDate,
            planExpiry: (user as any).planExpiry,
            storedActiveDays,
            calculatedActiveDays,
            discrepancy,
            manuallySet: (user as any).manuallySetActiveDays || false,
            lastUpdate: (user as any).lastActiveDaysUpdate
          })

          console.log(`User ${(user as any).email}: Stored=${storedActiveDays}, Calculated=${calculatedActiveDays}, Discrepancy=${discrepancy}`)
        } catch (error) {
          console.error(`Error processing user ${user.id}:`, error)
          results.push({
            userId: user.id,
            email: (user as any).email,
            error: (error as any).message
          })
        }
      }

      setDebugResults(results)
      console.log('✅ Debug check completed')

    } catch (error) {
      console.error('Error in debug check:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  const testFirebaseFunction = async () => {
    try {
      setIsTestingFirebaseFunction(true)

      console.log('🧪 Testing Firebase function after deployment...')

      // Test the optimized function that uses the fixed calculateActiveDaysForUser
      const result = await optimizedService.getDashboardData('current-user')

      if (result.userData) {
        Swal.fire({
          icon: 'success',
          title: 'Firebase Function Test Successful!',
          html: `
            <div class="text-left">
              <p><strong>User Active Days:</strong> ${result.userData.activeDays}</p>
              <p><strong>Plan:</strong> ${result.userData.plan}</p>
              <p><strong>Status:</strong> Firebase function is now using stored database values</p>
            </div>
          `,
          timer: 5000,
          showConfirmButton: true
        })
      } else {
        throw new Error('Function test failed')
      }

    } catch (error) {
      console.error('Error testing Firebase function:', error)
      Swal.fire({
        icon: 'error',
        title: 'Firebase Function Test Failed',
        text: 'There was an error testing the deployed function.',
      })
    } finally {
      setIsTestingFirebaseFunction(false)
    }
  }

  const analyzeActiveDaysIssue = async () => {
    try {
      setIsAnalyzing(true)
      setAnalysisResults(null)

      console.log('🔍 Analyzing active days issue across all users...')

      // Get all users to analyze the issue
      const allUsers = await getAllUsers()

      const analysis = {
        totalUsers: allUsers.length,
        trialUsers: 0,
        paidUsers: 0,
        suspiciouslyHighActiveDays: [] as any[],
        normalActiveDays: [] as any[],
        manuallySetUsers: [] as any[],
        recentlyJoinedButHighActiveDays: [] as any[]
      }

      const today = new Date()

      for (const user of allUsers) {
        const userData = user as any
        const activeDays = userData.activeDays || 0
        const plan = userData.plan || 'Unknown'
        const joinedDate = userData.joinedDate ? new Date(userData.joinedDate.seconds * 1000) : null
        const manuallySet = userData.manuallySetActiveDays || false

        // Count by plan type
        if (plan === 'Trial') {
          analysis.trialUsers++
        } else {
          analysis.paidUsers++
        }

        // Check for manually set users
        if (manuallySet) {
          analysis.manuallySetUsers.push({
            email: userData.email,
            plan,
            activeDays,
            joinedDate
          })
        }

        // Check for suspiciously high active days
        if (joinedDate) {
          const daysSinceJoined = Math.floor((today.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24))

          // If active days is much higher than days since joined, it's suspicious
          if (activeDays > daysSinceJoined + 5) { // Allow some buffer
            analysis.suspiciouslyHighActiveDays.push({
              email: userData.email,
              plan,
              activeDays,
              daysSinceJoined,
              joinedDate,
              difference: activeDays - daysSinceJoined
            })
          } else {
            analysis.normalActiveDays.push({
              email: userData.email,
              plan,
              activeDays,
              daysSinceJoined
            })
          }

          // Check for recently joined users with high active days
          if (daysSinceJoined <= 10 && activeDays > 15) {
            analysis.recentlyJoinedButHighActiveDays.push({
              email: userData.email,
              plan,
              activeDays,
              daysSinceJoined,
              joinedDate
            })
          }
        }
      }

      setAnalysisResults(analysis)
      console.log('✅ Analysis completed:', analysis)

      // Show summary
      Swal.fire({
        icon: 'info',
        title: 'Active Days Analysis Complete',
        html: `
          <div class="text-left">
            <p><strong>Total Users:</strong> ${analysis.totalUsers}</p>
            <p><strong>Trial Users:</strong> ${analysis.trialUsers}</p>
            <p><strong>Paid Users:</strong> ${analysis.paidUsers}</p>
            <p><strong>Suspicious High Active Days:</strong> ${analysis.suspiciouslyHighActiveDays.length}</p>
            <p><strong>Normal Active Days:</strong> ${analysis.normalActiveDays.length}</p>
            <p><strong>Manually Set by Admin:</strong> ${analysis.manuallySetUsers.length}</p>
          </div>
        `,
        showConfirmButton: true
      })

    } catch (error) {
      console.error('Error analyzing active days:', error)
      Swal.fire({
        icon: 'error',
        title: 'Analysis Failed',
        text: 'There was an error analyzing the active days data.',
      })
    } finally {
      setIsAnalyzing(false)
    }
  }

  const runActiveDaysRestoration = async () => {
    try {
      // First, confirm with user
      const confirmResult = await Swal.fire({
        icon: 'warning',
        title: 'Restore Active Days?',
        html: `
          <div class="text-left">
            <p><strong>⚠️ This will:</strong></p>
            <ul class="list-disc list-inside mt-2 space-y-1">
              <li>Analyze all users for incorrect active days</li>
              <li>Create a backup of current values</li>
              <li>Restore reasonable active days based on join dates</li>
              <li>Reset manual override flags</li>
            </ul>
            <p class="mt-4 text-red-600"><strong>This action cannot be easily undone!</strong></p>
          </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Yes, Restore Active Days',
        cancelButtonText: 'Cancel',
        confirmButtonColor: '#dc2626'
      })

      if (!confirmResult.isConfirmed) {
        return
      }

      setIsRestoring(true)

      console.log('🔧 Starting active days restoration process...')

      // Step 1: Create backup
      Swal.fire({
        title: 'Creating Backup...',
        text: 'Please wait while we backup current active days data.',
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading()
        }
      })

      const backup = await createActiveDaysBackup()
      if (!backup.success) {
        throw new Error(`Backup failed: ${backup.error}`)
      }

      // Step 2: Analyze what needs restoration
      Swal.update({
        title: 'Analyzing Users...',
        text: 'Analyzing all users to identify incorrect active days.'
      })

      const analysis = await analyzeAndRestoreActiveDays()

      if (analysis.needsRestoration.length === 0) {
        Swal.fire({
          icon: 'success',
          title: 'No Issues Found!',
          text: 'All users have reasonable active days values. No restoration needed.',
        })
        return
      }

      // Step 3: Show what will be restored and confirm
      const restoreConfirm = await Swal.fire({
        icon: 'question',
        title: `Found ${analysis.needsRestoration.length} Users to Restore`,
        html: `
          <div class="text-left">
            <p><strong>Users needing restoration:</strong> ${analysis.needsRestoration.length}</p>
            <p><strong>Total users analyzed:</strong> ${analysis.analyzed}</p>
            <div class="mt-4 max-h-40 overflow-y-auto bg-gray-50 p-3 rounded">
              ${analysis.needsRestoration.slice(0, 10).map((user: any) =>
                `<div class="text-sm mb-1">
                  <strong>${user.email}</strong> (${user.plan}): ${user.currentActiveDays} → ${user.suggestedActiveDays} days
                </div>`
              ).join('')}
              ${analysis.needsRestoration.length > 10 ? `<div class="text-sm text-gray-600">... and ${analysis.needsRestoration.length - 10} more</div>` : ''}
            </div>
          </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Proceed with Restoration',
        cancelButtonText: 'Cancel'
      })

      if (!restoreConfirm.isConfirmed) {
        return
      }

      // Step 4: Perform restoration
      Swal.fire({
        title: 'Restoring Active Days...',
        text: `Updating ${analysis.needsRestoration.length} users...`,
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading()
        }
      })

      const restoration = await restoreActiveDaysForUsers(analysis.needsRestoration)

      setRestorationResults({
        backup,
        analysis,
        restoration
      })

      // Step 5: Show results
      Swal.fire({
        icon: 'success',
        title: 'Active Days Restoration Complete!',
        html: `
          <div class="text-left">
            <p><strong>✅ Backup Created:</strong> ${backup.backupData.length} users</p>
            <p><strong>🔍 Users Analyzed:</strong> ${analysis.analyzed}</p>
            <p><strong>🔧 Users Restored:</strong> ${restoration.restored}</p>
            <p><strong>❌ Errors:</strong> ${restoration.errors.length}</p>
            ${restoration.errors.length > 0 ? `
              <div class="mt-2 text-red-600 text-sm">
                <strong>Errors:</strong><br>
                ${restoration.errors.slice(0, 3).join('<br>')}
                ${restoration.errors.length > 3 ? `<br>... and ${restoration.errors.length - 3} more` : ''}
              </div>
            ` : ''}
          </div>
        `,
        timer: 10000,
        showConfirmButton: true
      })

      console.log('✅ Active days restoration completed successfully')

    } catch (error) {
      console.error('Error in active days restoration:', error)
      Swal.fire({
        icon: 'error',
        title: 'Restoration Failed',
        text: `There was an error during restoration: ${error}`,
      })
    } finally {
      setIsRestoring(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner w-12 h-12 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to access this page.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">Debug Active Days</h1>
            <p className="text-gray-600 mt-2">
              Compare stored Firestore values vs calculated values for active days
            </p>
          </div>

          <div className="p-6">
            <div className="flex space-x-4 mb-6">
              <button
                onClick={runDebugCheck}
                disabled={isProcessing}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {isProcessing ? 'Processing...' : 'Run Debug Check (First 10 Users)'}
              </button>

              <button
                onClick={testFirebaseFunction}
                disabled={isTestingFirebaseFunction}
                className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50"
              >
                {isTestingFirebaseFunction ? 'Testing...' : 'Test Fixed Firebase Function'}
              </button>

              <button
                onClick={analyzeActiveDaysIssue}
                disabled={isAnalyzing}
                className="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 disabled:opacity-50"
              >
                {isAnalyzing ? 'Analyzing...' : 'Analyze Active Days Issue'}
              </button>

              <button
                onClick={runActiveDaysRestoration}
                disabled={isRestoring}
                className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 disabled:opacity-50"
              >
                {isRestoring ? 'Restoring...' : '🔧 Restore Active Days'}
              </button>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <i className="fas fa-info-circle text-yellow-600 mt-1"></i>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">Active Days Fix Status</h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <p>✅ <strong>Firebase Function Fixed:</strong> Now uses stored database values instead of recalculating</p>
                    <p>📊 <strong>Database Values:</strong> The stored activeDays in Firestore are correct</p>
                    <p>🔧 <strong>What Changed:</strong> Withdrawals page and admin functions now show accurate active days</p>
                  </div>
                </div>
              </div>
            </div>

            {analysisResults && (
              <div className="mt-8">
                <h2 className="text-lg font-semibold mb-4">Active Days Issue Analysis</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h3 className="font-medium text-blue-900">Total Users</h3>
                    <p className="text-2xl font-bold text-blue-600">{analysisResults.totalUsers}</p>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <h3 className="font-medium text-green-900">Normal Active Days</h3>
                    <p className="text-2xl font-bold text-green-600">{analysisResults.normalActiveDays.length}</p>
                  </div>
                  <div className="bg-red-50 p-4 rounded-lg">
                    <h3 className="font-medium text-red-900">Suspicious High</h3>
                    <p className="text-2xl font-bold text-red-600">{analysisResults.suspiciouslyHighActiveDays.length}</p>
                  </div>
                  <div className="bg-yellow-50 p-4 rounded-lg">
                    <h3 className="font-medium text-yellow-900">Manually Set</h3>
                    <p className="text-2xl font-bold text-yellow-600">{analysisResults.manuallySetUsers.length}</p>
                  </div>
                </div>

                {analysisResults.suspiciouslyHighActiveDays.length > 0 && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <h3 className="font-medium text-red-900 mb-3">🚨 Users with Suspiciously High Active Days</h3>
                    <div className="max-h-60 overflow-y-auto">
                      <table className="min-w-full">
                        <thead>
                          <tr>
                            <th className="text-left text-xs font-medium text-red-700 uppercase">Email</th>
                            <th className="text-left text-xs font-medium text-red-700 uppercase">Plan</th>
                            <th className="text-left text-xs font-medium text-red-700 uppercase">Active Days</th>
                            <th className="text-left text-xs font-medium text-red-700 uppercase">Days Since Joined</th>
                            <th className="text-left text-xs font-medium text-red-700 uppercase">Difference</th>
                          </tr>
                        </thead>
                        <tbody>
                          {analysisResults.suspiciouslyHighActiveDays.slice(0, 10).map((user: any, index: number) => (
                            <tr key={index} className="text-sm">
                              <td className="py-1 text-red-900">{user.email}</td>
                              <td className="py-1 text-red-900">{user.plan}</td>
                              <td className="py-1 font-bold text-red-600">{user.activeDays}</td>
                              <td className="py-1 text-red-900">{user.daysSinceJoined}</td>
                              <td className="py-1 font-bold text-red-600">+{user.difference}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                      {analysisResults.suspiciouslyHighActiveDays.length > 10 && (
                        <p className="text-sm text-red-600 mt-2">... and {analysisResults.suspiciouslyHighActiveDays.length - 10} more users</p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            {debugResults.length > 0 && (
              <div className="mt-8">
                <h2 className="text-lg font-semibold mb-4">Debug Results</h2>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          User
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Plan
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Stored Active Days
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Calculated Active Days
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Discrepancy
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Manually Set
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {debugResults.map((result, index) => (
                        <tr key={index} className={result.discrepancy ? 'bg-red-50' : ''}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{result.email}</div>
                            <div className="text-sm text-gray-500">{result.userId}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {result.plan}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {result.storedActiveDays}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {result.calculatedActiveDays}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {result.discrepancy ? (
                              <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                YES
                              </span>
                            ) : (
                              <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                NO
                              </span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {result.manuallySet ? 'Yes' : 'No'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
