"use strict";exports.id=1391,exports.ids=[1391,3772],exports.modules={91391:(t,a,e)=>{e.d(a,{CF:()=>l,I0:()=>w,TK:()=>u,getAdminDashboardStats:()=>d,getAllPendingWithdrawals:()=>L,getAllWithdrawals:()=>D,hG:()=>g,lo:()=>i,nQ:()=>E,r2:()=>h,updateWithdrawalStatus:()=>S,x5:()=>c});var o=e(75535),r=e(33784),s=e(3582);let n=new Map;async function d(){let t="dashboard-stats",a=function(t){let a=n.get(t);return a&&Date.now()-a.timestamp<3e5?a.data:null}(t);if(a)return a;try{let a=new Date;a.setHours(0,0,0,0);let e=o.Dc.fromDate(a),d=await (0,o.getDocs)((0,o.collection)(r.db,s.COLLECTIONS.users)),i=d.size,c=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.users),(0,o._M)(s.FIELD_NAMES.joinedDate,">=",e)),l=(await (0,o.getDocs)(c)).size,E=0,w=0,L=0,D=0;d.forEach(t=>{let e=t.data();E+=e[s.FIELD_NAMES.totalVideos]||0,w+=e[s.FIELD_NAMES.wallet]||0;let o=e[s.FIELD_NAMES.lastVideoDate]?.toDate();o&&o.toDateString()===a.toDateString()&&(L+=e[s.FIELD_NAMES.todayVideos]||0)});try{let t=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.transactions),(0,o._M)(s.FIELD_NAMES.type,"==","video_earning"),(0,o.AB)(1e3));(await (0,o.getDocs)(t)).forEach(t=>{let e=t.data(),o=e[s.FIELD_NAMES.date]?.toDate();o&&o>=a&&(D+=e[s.FIELD_NAMES.amount]||0)})}catch(t){console.warn("Could not fetch today's transactions:",t)}let h=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.withdrawals),(0,o._M)("status","==","pending")),u=(await (0,o.getDocs)(h)).size,g=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.withdrawals),(0,o._M)("date",">=",e)),S=(await (0,o.getDocs)(g)).size,p={totalUsers:i,totalVideos:E,totalEarnings:w,pendingWithdrawals:u,todayUsers:l,todayVideos:L,todayEarnings:D,todayWithdrawals:S};return n.set(t,{data:p,timestamp:Date.now()}),p}catch(t){throw console.error("Error getting admin dashboard stats:",t),t}}async function i(t=50,a=null){try{let e=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.users),(0,o.My)(s.FIELD_NAMES.joinedDate,"desc"),(0,o.AB)(t));a&&(e=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.users),(0,o.My)(s.FIELD_NAMES.joinedDate,"desc"),(0,o.HM)(a),(0,o.AB)(t)));let n=await (0,o.getDocs)(e);return{users:n.docs.map(t=>({id:t.id,...t.data(),joinedDate:t.data()[s.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:t.data()[s.FIELD_NAMES.planExpiry]?.toDate()})),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===t}}catch(t){throw console.error("Error getting users:",t),t}}async function c(t){try{if(!t||0===t.trim().length)return[];let a=t.toLowerCase().trim(),e=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.users),(0,o.My)(s.FIELD_NAMES.joinedDate,"desc"));return(await (0,o.getDocs)(e)).docs.map(t=>({id:t.id,...t.data(),joinedDate:t.data()[s.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:t.data()[s.FIELD_NAMES.planExpiry]?.toDate()})).filter(t=>{let e=String(t[s.FIELD_NAMES.name]||"").toLowerCase(),o=String(t[s.FIELD_NAMES.email]||"").toLowerCase(),r=String(t[s.FIELD_NAMES.mobile]||"").toLowerCase(),n=String(t[s.FIELD_NAMES.referralCode]||"").toLowerCase();return e.includes(a)||o.includes(a)||r.includes(a)||n.includes(a)})}catch(t){throw console.error("Error searching users:",t),t}}async function l(){try{let t=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.users),(0,o.My)(s.FIELD_NAMES.joinedDate,"desc"));return(await (0,o.getDocs)(t)).docs.map(t=>({id:t.id,...t.data(),joinedDate:t.data()[s.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:t.data()[s.FIELD_NAMES.planExpiry]?.toDate()}))}catch(t){throw console.error("Error getting all users:",t),t}}async function E(){try{let t=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.users));return(await (0,o.getDocs)(t)).size}catch(t){throw console.error("Error getting total user count:",t),t}}async function w(t=50,a=null){try{let e=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.transactions),(0,o.My)(s.FIELD_NAMES.date,"desc"),(0,o.AB)(t));a&&(e=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.transactions),(0,o.My)(s.FIELD_NAMES.date,"desc"),(0,o.HM)(a),(0,o.AB)(t)));let n=await (0,o.getDocs)(e);return{transactions:n.docs.map(t=>({id:t.id,...t.data(),date:t.data()[s.FIELD_NAMES.date]?.toDate()})),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===t}}catch(t){throw console.error("Error getting transactions:",t),t}}async function L(){try{console.log("\uD83D\uDD0D Loading ALL pending withdrawals...");let t=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.withdrawals),(0,o._M)("status","==","pending"),(0,o.My)("date","desc")),a=(await (0,o.getDocs)(t)).docs.map(t=>({id:t.id,...t.data(),date:t.data().date?.toDate()}));return console.log(`✅ Loaded ${a.length} pending withdrawals`),a}catch(t){throw console.error("Error getting all pending withdrawals:",t),t}}async function D(){try{console.log("\uD83D\uDD0D Loading ALL withdrawals...");let t=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.withdrawals),(0,o.My)("date","desc")),a=(await (0,o.getDocs)(t)).docs.map(t=>({id:t.id,...t.data(),date:t.data().date?.toDate()}));return console.log(`✅ Loaded ${a.length} total withdrawals`),a}catch(t){throw console.error("Error getting all withdrawals:",t),t}}async function h(){try{console.log("\uD83D\uDD0D Loading ALL transactions...");let t=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.transactions),(0,o.My)(s.FIELD_NAMES.date,"desc")),a=(await (0,o.getDocs)(t)).docs.map(t=>({id:t.id,...t.data(),date:t.data()[s.FIELD_NAMES.date]?.toDate()}));return console.log(`✅ Loaded ${a.length} total transactions`),a}catch(t){throw console.error("Error getting all transactions:",t),t}}async function u(t,a){try{await (0,o.mZ)((0,o.H9)(r.db,s.COLLECTIONS.users,t),a),n.delete("dashboard-stats")}catch(t){throw console.error("Error updating user:",t),t}}async function g(t){try{await (0,o.kd)((0,o.H9)(r.db,s.COLLECTIONS.users,t)),n.delete("dashboard-stats")}catch(t){throw console.error("Error deleting user:",t),t}}async function S(t,a,d){try{let i=await (0,o.x7)((0,o.H9)(r.db,s.COLLECTIONS.withdrawals,t));if(!i.exists())throw Error("Withdrawal not found");let{userId:c,amount:l,status:E}=i.data(),w={status:a,updatedAt:o.Dc.now()};if(d&&(w.adminNotes=d),await (0,o.mZ)((0,o.H9)(r.db,s.COLLECTIONS.withdrawals,t),w),"approved"===a&&"approved"!==E){let{addTransaction:t}=await Promise.resolve().then(e.bind(e,3582));await t(c,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${l} processed for transfer`})}if("rejected"===a&&"rejected"!==E){let{updateWalletBalance:t,addTransaction:a}=await Promise.resolve().then(e.bind(e,3582));await t(c,l),await a(c,{type:"withdrawal_rejected",amount:l,description:`Withdrawal rejected - ₹${l} credited back to wallet`})}n.delete("dashboard-stats")}catch(t){throw console.error("Error updating withdrawal status:",t),t}}}};